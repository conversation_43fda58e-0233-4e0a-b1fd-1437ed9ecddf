2025-07-08 17:53:07.544: [INFO] VisibilityChanged: 0
2025-07-08 17:53:07.589: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.590: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.590: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.591: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.592: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.593: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.594: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.594: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.595: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.596: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.596: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.607: [INFO] BookmarkModelLoaded, ids_reassigned: 0
2025-07-08 17:53:07.607: [INFO] OnDoneLoading sync enabled: 0
2025-07-08 17:53:07.660: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-07-08 17:53:07.739: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 3
2025-07-08 17:53:07.740: [INFO] Primary account changed.
2025-07-08 17:53:07.740: [INFO] OnPrimaryAccountChanged PrimaryAccountChangeEvent::Type::kSet
2025-07-08 17:53:07.743: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 3
2025-07-08 17:53:07.946: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 3
2025-07-08 17:53:10.277: [INFO] BookmarkNodeAdded, ABU: 0
2025-07-08 17:53:10.278: [INFO] BookmarkNodeAdded, ABU: 0
2025-07-08 17:53:10.278: [INFO] BookmarkNodeAdded, ABU: 0
2025-07-08 17:53:10.278: [INFO] BookmarkNodeAdded, ABU: 0
2025-07-08 17:53:10.934: [INFO] Sync initialization duration: 2904ms
