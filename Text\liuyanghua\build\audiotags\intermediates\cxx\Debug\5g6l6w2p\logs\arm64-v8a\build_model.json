{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android\\.cxx\\Debug\\5g6l6w2p\\arm64-v8a", "soFolder": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates\\cxx\\Debug\\5g6l6w2p\\obj\\arm64-v8a", "soRepublishFolder": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates\\cxx", "gradleModulePathName": ":audiotags", "moduleRootFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android", "moduleBuildFile": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android\\build.gradle", "makeFile": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393", "ndkVersion": "25.1.8937393", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 19, "max": 33, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\android", "sdkFolder": "D:\\QQ\\Android_Sdk\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "D:\\QQ\\Android_Sdk\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android\\.cxx\\Debug\\5g6l6w2p\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "5g6l6w2p4o1e8184s6p175o3l1769323c264f5el3qmwzu145w541l2i1o", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.1.0.\n#   - $NDK is the path to NDK 25.1.8937393.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HC:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/audiotags-1.4.5/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DC<PERSON>KE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/Users/<USER>/OneDrive/Desktop/hua/Cangjie-Examples/Text/liuyanghua/build/audiotags/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/Users/<USER>/OneDrive/Desktop/hua/Cangjie-Examples/Text/liuyanghua/build/audiotags/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-BC:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/audiotags-1.4.5/android/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_ANDROID_NDK=D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_TOOLCHAIN_FILE=D:\\QQ\\Android_Sdk\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\QQ\\Android_Sdk\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates\\cxx\\Debug\\5g6l6w2p\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates\\cxx\\Debug\\5g6l6w2p\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\android\\.cxx\\Debug\\5g6l6w2p\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\audiotags\\intermediates\\cxx\\Debug\\5g6l6w2p"}