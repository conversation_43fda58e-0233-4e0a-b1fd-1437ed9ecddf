{"inputs": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\flutter_build\\e7e739fb4ee519c6b05a0c88c2d93f1a\\app.dill", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\internal\\engine.version", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\internal\\engine.version", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\internal\\engine.version", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\pubspec.yaml", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\async-2.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_service-0.18.18\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_service_platform_interface-0.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_service_web-0.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\boolean_selector-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\build_cli_annotations-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\clock-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fake_async-1.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_rust_bridge-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\freezed_annotation-2.4.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\js-0.7.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\json_annotation-4.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio-0.9.46\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio_platform_interface-4.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio_web-0.4.16\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker-10.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker_flutter_testing-3.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\matcher-0.12.16+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\meta-1.15.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler-11.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_android-12.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_apple-9.4.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_html-0.1.3+5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_windows-0.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_android-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_darwin-2.4.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\stack_trace-1.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\test_api-0.7.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vm_service-14.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\LICENSE", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\LICENSE"], "outputs": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z"]}