{"logTime": "0708/175309", "correlationVector":"lc2fP0UWTMRFvMN6KgomzF.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900001j"}}
{"logTime": "0708/175309", "correlationVector":"lc2fP0UWTMRFvMN6KgomzF.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Encryption Keys", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0708/175309", "correlationVector":"7RexUrCKeqiWl3NWGaz/P3","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"", "migrationStage":"", "server":""}}
{"logTime": "0708/175309", "correlationVector":"7RexUrCKeqiWl3NWGaz/P3.0","action":"EncryptionKeysFetcher.DownloadKeys:", "result":"Success", "context":Key count: 5, Last key timestamp: 2025-06-08T03:40:27Z}
{"logTime": "0708/175309", "correlationVector":"7RexUrCKeqiWl3NWGaz/P3.1","action":"EncryptionKeysManager::SetEncryptionKeys:", "result":"Success", "context":Key names[5]:[fAvik8Fmu5BAiUPbGyL8UcD5Zfg27B/DfHlvkZj7hKDlXUe0m/n7TWfjThS5vsTWQSk1pynQokBWkQHyXV73mg==][2vytJUtQ9utMnoCxYZrMVIbs9zvCQ53FQBKuyJvCbFNq9SfOhfPRUtCfWXk4AOolleK7MAF3c9LfWetAx5odZA==][b7HznMWYewsZReUjm+X8mU9dviwsZEWzFmGaNAYWnfhybjI/YvSRaaURZJBeDmm9jXpKJd1DOkQahO5d2Cji2A==][apylGJ0w7lrO4NlccgOAbhSXHc/sQUhaJsYnXIW6lzhuUbmqAghPjApZb8yHo89NK/5by2wVogc7Z+Hq/M0blQ==][jwvaP2Li7Xki3Fp4i/gGmwNvRR+b40/uaVxmbSzfPTdCegNGQSlgc+BcTRMJ4mnXCpyn+1n+YrLI86fx+8DpBw==]}
{"logTime": "0708/175309", "correlationVector":"7RexUrCKeqiWl3NWGaz/P3.2","action":"EncryptionKeysManager::SetEncryptionKeysWithTimestamps:", "result":"Success", "context":Key timestamps[5]:[2023-09-05T10:21:56Z][2024-03-04T05:06:40Z][2024-09-02T00:32:58Z][2024-12-09T12:09:56Z][2025-06-08T03:40:27Z]}
{"logTime": "0708/175309", "correlationVector":"lc2fP0UWTMRFvMN6KgomzF","action":"Initial GetUpdates", "result":"", "context":Reason: NEW_CLIENT. cV=lc2fP0UWTMRFvMN6KgomzF}
{"logTime": "0708/175309", "correlationVector":"lc2fP0UWTMRFvMN6KgomzF.3","action":"GetUpdates Response", "result":"Success", "context":Received 1 update(s). cV=lc2fP0UWTMRFvMN6KgomzF.0;server=akswtt00900001j;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175309", "correlationVector":"27XtGOfFiUTdLPyDj9MG1j","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=27XtGOfFiUTdLPyDj9MG1j}
{"logTime": "0708/175310", "correlationVector":"27XtGOfFiUTdLPyDj9MG1j.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt009000029"}}
{"logTime": "0708/175310", "correlationVector":"27XtGOfFiUTdLPyDj9MG1j.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"82", "total":"82"}}
{"logTime": "0708/175310", "correlationVector":"27XtGOfFiUTdLPyDj9MG1j.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"30", "total":"30"}}
{"logTime": "0708/175310", "correlationVector":"27XtGOfFiUTdLPyDj9MG1j.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0708/175310", "correlationVector":"27XtGOfFiUTdLPyDj9MG1j.5","action":"GetUpdates Response", "result":"Success", "context":Received 114 update(s). cV=27XtGOfFiUTdLPyDj9MG1j.0;server=akswtt009000029;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175310", "correlationVector":"OzXYIeo7NtclPHz0xIwqH0","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=OzXYIeo7NtclPHz0xIwqH0}
{"logTime": "0708/175310", "correlationVector":"OzXYIeo7NtclPHz0xIwqH0.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900000d"}}
{"logTime": "0708/175310", "correlationVector":"OzXYIeo7NtclPHz0xIwqH0.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Bookmarks", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"9", "total":"9"}}
{"logTime": "0708/175310", "correlationVector":"OzXYIeo7NtclPHz0xIwqH0.3","action":"GetUpdates Response", "result":"Success", "context":Received 9 update(s). cV=OzXYIeo7NtclPHz0xIwqH0.0;server=akswtt00900000d;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=a3m+SBpaOJtOLsHMVBW/7G}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900000e"}}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"6", "total":"6"}}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"88", "total":"88"}}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"149", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"149", "total":"149"}}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"6", "total":"6"}}
{"logTime": "0708/175310", "correlationVector":"a3m+SBpaOJtOLsHMVBW/7G.7","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=a3m+SBpaOJtOLsHMVBW/7G.0;server=akswtt00900000e;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted Some updates remain.}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=Sb6JAmrRlVQTCpIQ1knMgy}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900001y"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"16", "total":"16"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"160", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"164", "total":"164"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extension settings", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0708/175310", "correlationVector":"Sb6JAmrRlVQTCpIQ1knMgy.8","action":"GetUpdates Response", "result":"Success", "context":Received 191 update(s). cV=Sb6JAmrRlVQTCpIQ1knMgy.0;server=akswtt00900001y;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175310", "correlationVector":"AO2/GpvCldPm0uuGGxD3G/","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=AO2/GpvCldPm0uuGGxD3G/}
{"logTime": "0708/175311", "correlationVector":"AO2/GpvCldPm0uuGGxD3G/.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900001f"}}
{"logTime": "0708/175311", "correlationVector":"AO2/GpvCldPm0uuGGxD3G/.2","action":"GetUpdates Response", "result":"Success", "context":Received 0 update(s). cV=AO2/GpvCldPm0uuGGxD3G/.0;server=akswtt00900001f;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175311", "correlationVector":"p6toHvqcInisV4rEMUobMx","action":"Normal GetUpdate request", "result":"", "context":cV=p6toHvqcInisV4rEMUobMx
Nudged types: Preferences, Sessions, Device Info, User Consents
Refresh requested types: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys}
{"logTime": "0708/175311", "correlationVector":"p6toHvqcInisV4rEMUobMx.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900001f"}}
{"logTime": "0708/175311", "correlationVector":"p6toHvqcInisV4rEMUobMx.2","action":"GetUpdates Response", "result":"Success", "context":Received 0 update(s). cV=p6toHvqcInisV4rEMUobMx.0;server=akswtt00900001f;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175311", "correlationVector":"E1+l7FK8MYHsFz0bn7PI3c","action":"Commit Request", "result":"", "context":Item count: 6
Contributing types: Preferences, Sessions, Device Info, User Consents}
{"logTime": "0708/175311", "correlationVector":"E1+l7FK8MYHsFz0bn7PI3c.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900001d"}}
{"logTime": "0708/175311", "correlationVector":"E1+l7FK8MYHsFz0bn7PI3c.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=E1+l7FK8MYHsFz0bn7PI3c.0;server=akswtt00900001d;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
{"logTime": "0708/175312", "correlationVector":"BSNsGrqgfX140vecieauxp","action":"Commit Request", "result":"", "context":Item count: 1
Contributing types: Edge Hub App Usage}
{"logTime": "0708/175312", "correlationVector":"BSNsGrqgfX140vecieauxp.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southeastasia_prod-s01-065-apac-southeastasia", "migrationStage":"NotStarted", "server":"akswtt00900001w"}}
{"logTime": "0708/175312", "correlationVector":"BSNsGrqgfX140vecieauxp.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=BSNsGrqgfX140vecieauxp.0;server=akswtt00900001w;cloudType=Consumer;environment=Prod_southeastasia_prod-s01-065-apac-southeastasia;migrationStage=NotStarted}
