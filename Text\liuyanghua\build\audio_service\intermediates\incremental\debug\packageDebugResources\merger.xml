<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res"><file name="audio_service_fast_forward" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_fast_forward.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_fast_rewind" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_fast_rewind.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_pause" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_pause.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_play_arrow" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_play_arrow.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_skip_next" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_skip_next.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_skip_previous" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_skip_previous.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_stop" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-hdpi\audio_service_stop.png" qualifiers="hdpi-v4" type="drawable"/><file name="audio_service_fast_forward" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_fast_forward.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_fast_rewind" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_fast_rewind.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_pause" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_pause.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_play_arrow" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_play_arrow.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_skip_next" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_skip_next.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_skip_previous" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_skip_previous.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_stop" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-mdpi\audio_service_stop.png" qualifiers="mdpi-v4" type="drawable"/><file name="audio_service_fast_forward" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_fast_forward.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_fast_rewind" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_fast_rewind.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_pause" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_pause.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_play_arrow" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_play_arrow.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_skip_next" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_skip_next.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_skip_previous" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_skip_previous.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_stop" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xhdpi\audio_service_stop.png" qualifiers="xhdpi-v4" type="drawable"/><file name="audio_service_fast_forward" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_fast_forward.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_fast_rewind" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_fast_rewind.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_pause" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_pause.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_play_arrow" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_play_arrow.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_skip_next" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_skip_next.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_skip_previous" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_skip_previous.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_stop" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxhdpi\audio_service_stop.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="audio_service_fast_forward" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_fast_forward.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="audio_service_fast_rewind" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_fast_rewind.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="audio_service_pause" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_pause.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="audio_service_play_arrow" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_play_arrow.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="audio_service_skip_next" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_skip_next.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="audio_service_skip_previous" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_skip_previous.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="audio_service_stop" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\main\res\drawable-xxxhdpi\audio_service_stop.png" qualifiers="xxxhdpi-v4" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.flutter-io.cn\audio_service-0.18.18\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\build\audio_service\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\build\audio_service\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>