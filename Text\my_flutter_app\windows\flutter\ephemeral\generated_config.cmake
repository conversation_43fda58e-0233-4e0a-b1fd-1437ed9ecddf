# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\my_flutter_app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\QQ\\flutter_windows_3.27.4-stable\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\my_flutter_app"
  "FLUTTER_ROOT=D:\\QQ\\flutter_windows_3.27.4-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\my_flutter_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\my_flutter_app"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\my_flutter_app\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\my_flutter_app\\.dart_tool\\package_config.json"
)
