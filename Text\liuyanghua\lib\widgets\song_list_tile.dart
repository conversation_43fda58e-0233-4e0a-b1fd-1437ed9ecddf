import 'package:flutter/material.dart';
import '../models/song.dart';

/// 歌曲列表项组件
class SongListTile extends StatelessWidget {
  final Song song;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final Widget? trailing;
  final bool isPlaying;

  const SongListTile({
    super.key,
    required this.song,
    this.onTap,
    this.onFavoriteToggle,
    this.trailing,
    this.isPlaying = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListTile(
      leading: _buildLeading(context),
      title: Text(
        song.title,
        style: TextStyle(
          fontWeight: isPlaying ? FontWeight.bold : FontWeight.normal,
          color: isPlaying ? colorScheme.primary : null,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        '${song.artist} • ${song.album}',
        style: TextStyle(
          color: isPlaying ? colorScheme.primary.withOpacity(0.7) : null,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            song.formattedDuration,
            style: theme.textTheme.bodySmall,
          ),
          const SizedBox(width: 8),
          if (onFavoriteToggle != null)
            IconButton(
              icon: Icon(
                song.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: song.isFavorite ? Colors.red : null,
              ),
              onPressed: onFavoriteToggle,
            ),
          if (trailing != null) trailing!,
        ],
      ),
      onTap: onTap,
    );
  }

  /// 构建前导图标
  Widget _buildLeading(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isPlaying) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: colorScheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.music_note,
          color: colorScheme.onPrimary,
        ),
      );
    }

    // 尝试显示专辑封面，如果没有则显示默认图标
    if (song.albumArt != null && song.albumArt!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          song.albumArt!,
          width: 48,
          height: 48,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildDefaultIcon(context);
          },
        ),
      );
    }

    return _buildDefaultIcon(context);
  }

  /// 构建默认图标
  Widget _buildDefaultIcon(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.music_note,
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }
}
