1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.liuyanghua"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- 音频播放相关权限 -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:3:5-80
17-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:3:22-77
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:4:5-81
18-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:4:22-78
19    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
19-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:5:5-75
19-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:5:22-72
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:6:5-68
20-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:6:22-65
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:7:5-77
21-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:7:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
22-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:8:5-92
22-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:8:22-89
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:47:5-52:15
31        <intent>
31-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:48:9-51:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:49:13-72
32-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:49:21-70
33
34            <data android:mimeType="text/plain" />
34-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:50:13-50
34-->C:\Users\<USER>\OneDrive\Desktop\hua\Cangjie-Examples\Text\liuyanghua\android\app\src\main\AndroidManifest.xml:50:19-48
35        </intent>
36    </queries>
37
38    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
38-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ea763f08590e45b5a76c25471423bc7a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
38-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\ea763f08590e45b5a76c25471423bc7a\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.example.liuyanghua.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.example.liuyanghua.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
47        android:name="android.app.Application"
48        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
49        android:debuggable="true"
50        android:extractNativeLibs="true"
51        android:icon="@mipmap/ic_launcher"
52        android:label="liuyanghua" >
53        <activity
54            android:name="com.example.liuyanghua.MainActivity"
55            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
56            android:exported="true"
57            android:hardwareAccelerated="true"
58            android:launchMode="singleTop"
59            android:taskAffinity=""
60            android:theme="@style/LaunchTheme"
61            android:windowSoftInputMode="adjustResize" >
62
63            <!--
64                 Specifies an Android theme to apply to this Activity as soon as
65                 the Android process has started. This theme is visible to the user
66                 while the Flutter UI initializes. After that, this theme continues
67                 to determine the Window background behind the Flutter UI.
68            -->
69            <meta-data
70                android:name="io.flutter.embedding.android.NormalTheme"
71                android:resource="@style/NormalTheme" />
72
73            <intent-filter>
74                <action android:name="android.intent.action.MAIN" />
75
76                <category android:name="android.intent.category.LAUNCHER" />
77            </intent-filter>
78        </activity>
79        <!--
80             Don't delete the meta-data below.
81             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
82        -->
83        <meta-data
84            android:name="flutterEmbedding"
85            android:value="2" />
86
87        <uses-library
87-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
88            android:name="androidx.window.extensions"
88-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
89            android:required="false" />
89-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
90        <uses-library
90-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
91            android:name="androidx.window.sidecar"
91-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
92            android:required="false" />
92-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
93
94        <provider
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
96            android:authorities="com.example.liuyanghua.androidx-startup"
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <receiver
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
107            android:name="androidx.profileinstaller.ProfileInstallReceiver"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
108            android:directBootAware="false"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
109            android:enabled="true"
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
110            android:exported="true"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
111            android:permission="android.permission.DUMP" >
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
113                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
116                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
119                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
122                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
123            </intent-filter>
124        </receiver>
125    </application>
126
127</manifest>
