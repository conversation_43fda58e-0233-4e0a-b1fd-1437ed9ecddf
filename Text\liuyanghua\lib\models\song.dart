/// 歌曲数据模型
class Song {
  final int? id;
  final String title;
  final String artist;
  final String album;
  final String filePath;
  final int duration; // 持续时间（秒）
  final String? albumArt; // 专辑封面路径
  final DateTime dateAdded;
  final bool isFavorite;

  Song({
    this.id,
    required this.title,
    required this.artist,
    required this.album,
    required this.filePath,
    required this.duration,
    this.albumArt,
    DateTime? dateAdded,
    this.isFavorite = false,
  }) : dateAdded = dateAdded ?? DateTime.now();

  /// 从数据库Map创建Song对象
  factory Song.fromMap(Map<String, dynamic> map) {
    return Song(
      id: map['id'],
      title: map['title'] ?? 'Unknown Title',
      artist: map['artist'] ?? 'Unknown Artist',
      album: map['album'] ?? 'Unknown Album',
      filePath: map['file_path'],
      duration: map['duration'] ?? 0,
      albumArt: map['album_art'],
      dateAdded: DateTime.parse(map['date_added']),
      isFavorite: map['is_favorite'] == 1,
    );
  }

  /// 转换为数据库Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'artist': artist,
      'album': album,
      'file_path': filePath,
      'duration': duration,
      'album_art': albumArt,
      'date_added': dateAdded.toIso8601String(),
      'is_favorite': isFavorite ? 1 : 0,
    };
  }

  /// 创建副本并修改某些属性
  Song copyWith({
    int? id,
    String? title,
    String? artist,
    String? album,
    String? filePath,
    int? duration,
    String? albumArt,
    DateTime? dateAdded,
    bool? isFavorite,
  }) {
    return Song(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      albumArt: albumArt ?? this.albumArt,
      dateAdded: dateAdded ?? this.dateAdded,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  /// 格式化持续时间为 mm:ss 格式
  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  String toString() {
    return 'Song{id: $id, title: $title, artist: $artist, album: $album}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Song &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          filePath == other.filePath;

  @override
  int get hashCode => id.hashCode ^ filePath.hashCode;
}
