import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/music_provider.dart';
import '../widgets/mini_player.dart';
import 'music_library_screen.dart';
import 'playlists_screen.dart';
import 'favorites_screen.dart';
import 'player_screen.dart';

/// 主屏幕 - 包含底部导航和迷你播放器
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const MusicLibraryScreen(),
    const PlaylistsScreen(),
    const FavoritesScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // 初始化音乐提供者
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MusicProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 主要内容区域
          Expanded(
            child: _screens[_currentIndex],
          ),
          // 迷你播放器
          Consumer<MusicProvider>(
            builder: (context, musicProvider, child) {
              if (musicProvider.currentSong != null) {
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PlayerScreen(),
                      ),
                    );
                  },
                  child: const MiniPlayer(),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.library_music),
            label: '音乐库',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.playlist_play),
            label: '歌单',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: '收藏',
          ),
        ],
      ),
    );
  }
}
