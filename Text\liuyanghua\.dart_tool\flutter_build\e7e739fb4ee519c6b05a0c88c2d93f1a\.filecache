{"version": 2, "files": [{"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "hash": "265caf611dc4dfb55e5713c3c7e87869"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\lib\\src\\core.dart", "hash": "f3c5cbf9ea9b5cf496bac755beda2220"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "hash": "b69e902366941432c473f910ead033c5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "hash": "34db9d7c9ebc27ae8cf7b0284f83365e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "hash": "b0c53ba7f6749cfea50c4f52b9413546"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "hash": "ff00114af99315cf1ab0d63e81be0fae"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "hash": "41402052ab3d1572da3568ba99f438b1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "hash": "b712ccf274e1965ba296f5e95416fe0c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "hash": "c4c4bd8e07f912e6829d0f9c85df3e73"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "hash": "90a93105383816828fa285fc36bb4639"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "hash": "fb9b6486b247c0214f3f09375cf29e2e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "hash": "b5b2a5b98fbd6ae3120fcd476e5553ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audiotags-1.4.5\\LICENSE", "hash": "542584ad8d5079a1114a32bf4b9baabc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "hash": "ba0c8dd8a234a449050e76d22738480f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "hash": "8c6dc0febf45193d83a3433e551832dc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "hash": "e5dfdce2a4625bd54ba39a9d4db60d2a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\mixin\\import_mixin.dart", "hash": "cd0157df37359679426daecbd76dfcc5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "hash": "6ac7ec254a9772032f8ea42b034074d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "hash": "4c6bcdf727cb9cbdac7621ac48198d26"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "hash": "b895dd95937a0aaf786505df4fa0e5fb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "hash": "c55cb4d008ae470c3d94aaffe529afe4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "hash": "d943487c044ce6ac8e7cd3d2fa81339b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "hash": "b07c3189c0eb5674a90a361c4a349bc5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "hash": "bcb87612b1f2033d529fa36128001f1f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "hash": "9a7eb049bd2c35586b8ef26b0e9d4bfa"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "hash": "0afbdd6f1125195ec28ff55922e51d50"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_android-2.4.0\\lib\\sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "hash": "8ac15481cfd33dc6982a43e2682b191d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\lib\\audio_session.dart", "hash": "8aaf3b69fb45264a9ff7160596f4281d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "hash": "518b8af2e4d78836a3e24f783f3a46be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\database_mixin.dart", "hash": "8643e5c3ed4cc326261faa439ab41eaa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "hash": "220771badc11e64d7d8a61a76737d0d8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "hash": "2138ff863db811644480df0504afa34c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "hash": "1a497614f0ada83403f1b356dceb0fc8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "hash": "61eb2619c9992fe3ed9694462cf005f8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "hash": "39c2ed3b6f1f3bfad95577df113d3527"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "hash": "5ca03d4075489c2401bed7afd621a0ff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\lib\\src\\utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "hash": "9424c326332bab32b9b324f84728390c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "hash": "ef24941070c1a5e277ef84875c481aa1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "hash": "5f6e776eee0d981c019ec748223ccbee"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "hash": "4017638906d150b0b0094be533549ac1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "hash": "e88bebb28589bba52a35fa4901ef8958"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "hash": "15d7f1a7aed94123635737479c2956a0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "hash": "a52c78806acbf2086461eddef9debc70"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_darwin-2.4.1+1\\lib\\sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "hash": "354bdd7dd0e0fa6af97450d357e6122c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler-11.4.0\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\src\\method_channel\\utils\\codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "hash": "2d725890566a0e72dff755f814e6f812"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "hash": "b4eba6b68edfa93319d2263690902196"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\meta-1.15.0\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "hash": "1650055881c20cece4ca2c0226e3946b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "hash": "f23f3b6a2033a262309d814b0d963131"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "hash": "0582a8b20dfce1c8219289afbf0c0e09"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "hash": "dab4500e9b0bd7c989e934028382d55c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "hash": "55e2bfe8be9272daed91a206b48a4584"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "hash": "5e65f1773fa878ab91fffc6b574ac082"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "hash": "1f2c17ade0582e6a0b832896261f0ea1"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\screens\\playlists_screen.dart", "hash": "0588608347e72edc7084329f5d063568"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "hash": "f6119810bfa347886bb08f6ed6168017"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "hash": "e7efbf97bcf6fe5ecce2f382c99c05c5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "hash": "cd1716f648982d9d210c1d58ca2f3d28"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\lib\\src\\allocation.dart", "hash": "7c8e196c6c96efaadb0e605ec3cb1cce"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "hash": "217b7c2fd7b1eccde5897e1f17fdccf9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "hash": "a0308b2c60e596d8d584f41cbc00ddc1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "hash": "92dc5c1071170d35b5bb9e7d4c5e362c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "hash": "d000c729fee4c177de714a210f1b4332"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "hash": "498bd94bc442029b68abbbfafd1771d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\sqflite_darwin.dart", "hash": "00e989c051be91287e0da3bd26a753af"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_darwin-2.4.1+1\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fake_async-1.3.1\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "hash": "aaa9b32ad52cf3b2651efe017635524f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\combined_wrappers\\combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "hash": "d73eb52cadc4b09d23b07deeaf12936e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "hash": "6dc0c45175212bb5e5ce131e28fa7d30"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\sqlite_api.dart", "hash": "0ced28711559707743bc05c76130e6e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "hash": "dccea2e51f83ecc016a1eb377f9e1c91"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "hash": "1586f5d24f300541caebb7e008342e25"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "hash": "2e66abe58a6cd5b05adaa08aab35773d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "hash": "601ae765c93d30071ed9300377d233e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "hash": "ac7b5ec3363036cb9b72ad279b3632d3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "hash": "f000fa0783142670ffdc6037e76f0bb9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "hash": "c249f78dd9406efb6956af4486ff1639"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "hash": "afd1b6d1bb872d5686b5057a7802a504"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "hash": "d0e444db0f838c4776afee5b98c845d0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_service-0.18.18\\LICENSE", "hash": "592634b0f8232efae5b1136f1b39c87d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "hash": "e9ad358e52cc0f7699f9196ce3e35da9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\src\\service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "hash": "922114742817940a9fbc560c67208459"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "hash": "eb0361b270543f45fe1841c22539289f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\freezed_annotation-2.4.4\\LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\animation.dart", "hash": "e76c07fd6945a4eadb2aeebf87b643bd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "hash": "f255aed97911507289cb6f5da97da9e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_windows-0.2.1\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\gestures.dart", "hash": "7b6199dff5808c0464b865fe03c4c616"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "hash": "204418884d1e655617fe131a7af13d5a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "hash": "99ec0197ee2762a7a084fb8f83c2adcd"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\flutter_build\\e7e739fb4ee519c6b05a0c88c2d93f1a\\native_assets.dill", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "hash": "43086faa74e4ad7bdb369be8d6fc496b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "hash": "acf61b3b8c12e6b09618384fbbc2e349"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "hash": "c15b0c06c9817813063ea4c1023d57fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\meta-1.15.0\\lib\\meta.dart", "hash": "8042ca366a626884c0e89628875af940"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "hash": "905f454a2c877ea4aaa79e5a73e97df9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "hash": "40e543d94e5319e7f325db394483a5cc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "hash": "1578c11ca57f922c046c6f48f3e822b2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "hash": "071cfd76b80374973cf6bf48876490aa"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "hash": "64bfc618a25eaa15e0decb3ff846b687"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "hash": "802ccedc203bfc23144dbb1905e0730d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "hash": "b6ba4d74e3970a0f3f1c409f604a808b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "hash": "04cf389a619cdfb5a15cebaa4b49fb1b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "hash": "0ae07a14319c9f730c284544e8225f35"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "hash": "d6d40aa3365829428ed28a579c3f7e6a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "hash": "3bc578f6d7cf48c66b27fe20889c0ee2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "hash": "a5c26e493259d75a046318603212e53f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "hash": "e988d89a54eaa8e63be51d46c0bd10e7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "hash": "6e9369c157f34f7559579cee1f30b933"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\factory_mixin.dart", "hash": "25c9f7df4c862471f534b041fc4213b8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "hash": "10fedd75e9b6e5d8bd58d4005c62f8e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "hash": "f40980afb2c03793cde4d218b7dfe6ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio_platform_interface-4.5.0\\lib\\method_channel_just_audio.dart", "hash": "9299b7019ded48cdc6b1cf5898ceb98a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "hash": "e37f788cd936291df67960229387622b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "hash": "1d1d0a67f0f70ea092507986657041e9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "hash": "32625e5104eba074fc381f3fb06cb176"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio-0.9.46\\lib\\just_audio.dart", "hash": "7b8a0192de0b5cfc351ba6b4bfd87233"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "hash": "6a5fb31099e1a0a55e279cc88a49f869"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\physics.dart", "hash": "ffd7e9991334466f08df7afe0c721048"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "hash": "8cb875a5ca649498461420066ef034e4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "hash": "8a469af03823d225decc71e75b4f909c"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\models\\song.dart", "hash": "b52b57b99c43af83ed8cb6f9e427f7ba"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "hash": "418fbf7b04705fde96067ac9e2faea6c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "hash": "7cb404d051d483abbff448707069ad18"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "hash": "a05b35e402965727a6b38bfc20b6ea15"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "hash": "92a9fb5bbb4d731698176978af361780"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "hash": "f5820ed92a76bbb5a33c3b0149db5190"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "hash": "59cee154986a022364494052f8eb3abc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "hash": "e98b1a4d5852efe3fd90f32f1d1175ad"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "hash": "9a11428dbc3420053cee3d1bad29fa66"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "hash": "2863f4964cfc96bab51ba3f6180a469c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "hash": "47474102c009e7099f3a9bf1d7ea8e06"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "hash": "c312ce71f146965cc9fce898eaf8dfc9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "hash": "b9594cab36a0f304b824062803918631"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "hash": "a5a4188942d047911977bd2c4e62cc9d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "hash": "13626b33f6c9e00af53759a1d6cb3882"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "hash": "44a49fff6e3307b5b7dc7b28fec8d559"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "hash": "2ed82d0ee4672e99dcdec5652737899f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "hash": "69e5a7f8f485b93b091d65670f4d8cd5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "hash": "3ac93d19ff74a097d33c55772f087514"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "hash": "7a54d34195b3233cdeefde3e98d5eac9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\utils\\utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "hash": "f2e6d3978ff6b0348b48e4f10cc97b69"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "hash": "b0a92f0ae1bab2ddfd1b64be675e9f4a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "hash": "fbd25dba106a5b33c4ade37a17dd0896"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "hash": "05019db2e6b03c3df79e0f3c002a0734"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "hash": "a5e824b3eedf3261e95c1143c4e9b157"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\src\\typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "hash": "5af5a2fb59f9c015abbda254a35ea7a6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "hash": "c2ec1d6c917cb9e9e0e0a42a1c03499e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "hash": "b259ece4ff8ce05e9b1bcaf0415fe1b6"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "hash": "6d10558fefdc90f0f18050bdc02f2447"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "hash": "84dda94c7115a229817d893bba6cd260"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "hash": "fafec099d30811a6f9c891fb2e32cef5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "hash": "1e59a7638a8f73d60d63296be7d9b008"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "hash": "3ed2c4934c2b01c8a929e0fe34b79da5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "hash": "33dd8457b8d5e71c4c375acf8f6e2654"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "hash": "971e19843c122686585b3ed5fb15edb5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "hash": "bee2b2312d8a87cbe17570d2bf872905"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "hash": "7f080cc2e66197a8144480b5f72b5e9e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\js-0.7.1\\LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "hash": "18dc29748f8511a8ab75e9bf213d01b6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "hash": "cf0267e98e801aaa2cc53e262cb6775a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "hash": "ae9019c399d97918691a06f69335df62"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "hash": "dbf829c2300f100c1301accafb7b4ff3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "hash": "c8ab5bd48c3c29edf5537624074283b5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "hash": "4c427b44efd5bee14d1e679725b3b090"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "hash": "842ac7528eff5cc84b6c6966ddb6f725"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "hash": "8a3b8d27019d16b6db69fc464749b063"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "hash": "08e8b12560cc4d70b1db6cae8905ed3a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\xdg_directories-1.1.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "hash": "2b25f08ee6552ace87c0219a40104d71"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "hash": "3fe2706914ad17c39b43be4941cd6de7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "hash": "f0a6c2c82fbb26b2048a287314e16723"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "hash": "331e0eb256b0b35c78d828a89df5d98f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "hash": "282511ab9573529eb3f7063d9b5f325d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "hash": "68a0f7290f162350227a7a69eabb6f3b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "hash": "a8db81c1bc3a26276fa505d6b92d1a67"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "hash": "50d7992f1dda62d9e1cdc11fb5fe193f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "hash": "b8ada56a9afce1f00e08e7b021bcb8bc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "hash": "bd09fd6d93e09b6bf66387eaba635795"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "hash": "36860581ee3e2aa766941e30d7974cc5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "hash": "99248376b2bf36c7eb7ae5934cefbae3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "hash": "0ba3b253278406a3b4805ce1a93884a2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "hash": "bb5b3736ed5ea3fd53c034ef8e986c85"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "hash": "da158fa37cde951754a18e969f569e29"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "hash": "399a2d652d314be315778a3680a2e17d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "hash": "89d9725151845780e6ab907034a8d87c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio-0.9.46\\LICENSE", "hash": "e9898cbd2824d4e3dfa968f76821ca50"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "hash": "9bbdb2b8645420b0dab017ab8a9126a7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "hash": "624431304ab3076b73b09e0b33e35e4f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "hash": "acf73f4397d15e5e31f1a837c598e825"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "hash": "fe17a06eb234271db2cc2d87de581e8e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "hash": "ff126fe6a01cdd6a8b2135f103cc1cb5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\sqflite.dart", "hash": "b78501a68c4c0d06da54deaa232576a6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "hash": "af4df0d955cfa425e4fbcdc5cee6a376"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "hash": "c77ed440a57bb90c0cb822a2225816cd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "hash": "e7fb312c35140edf8e552b6ee73245cd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\collection_utils.dart", "hash": "2b1435db1169d8d724de87f5054c89c8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\platform\\platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "hash": "b4dcd9e8e8e23d189c15921b446f540f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "hash": "e35a2cbb5404ae6e9de304ea27cb552a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "hash": "68fa2747d846becb6cae2bd247a1f86d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "hash": "70a64a732511203f21a08dae3b31c73c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "hash": "20eb6d7f0dac2a803c6623a3beb6838c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\painting.dart", "hash": "7b5d0b987cc8c397c881d50069014242"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\sqflite_android.dart", "hash": "ec562f20505ab846c07aae091a58b990"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "hash": "74575d6b9581ed5e99ce154012a277e4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "hash": "6c49f36c204d9c39ed8344462e4342f9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "hash": "e1dd5aed7cb9a731106f2a200c99fd42"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "hash": "df0a9878a16d3cd73cff00f496307544"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "hash": "3dfd7fabfa14947ea1cbb90517128458"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "hash": "723b1f84c494ae7ca65424e4e636d8e5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "hash": "2c3e9e4cb3ab51f5b350b9cc496654d6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "hash": "83f3fe2da23a7a3b644ef2c8ea858d97"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "hash": "b708db8bcf69fa54c230fb82654ee253"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "hash": "2ce77f3493d9ffbe36ca1913ffa684b8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "hash": "a384df45eb810f3784e52458ba693b1d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\batch.dart", "hash": "34c2a18e24c9d0bc442ae2559a4d7479"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker_flutter_testing-3.0.8\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "hash": "19a48bb0fdc91f4bb4b02ac591e52875"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "hash": "b1ac6c9a9a7a04cd140060d4f93dc1fe"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "hash": "97a82a0195c80b82e017af9bc615740e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "hash": "ad788d22811412b6221c7de5e6baadc5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\sql_builder.dart", "hash": "80794af0b4b1fe6f3b36e594121e3813"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "hash": "d6a363bef9b8a8ecea3052c28c5c5146"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "hash": "db6de1276bb93466aaa6b8fe04e747ee"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "hash": "f183006309c1b04d3fc4ee6f8692eeab"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "hash": "a73cb055124ccdbd41dc97e9c4d05191"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "hash": "bdc7e70e7c2adca035ebff42e23dfae8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "hash": "da83da0ebcda66761450f830150bc529"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\open_options.dart", "hash": "0c3c36af8e9790ab80e8790b68cd84a8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "hash": "e8e2e9fcc45e6624f8135fdef9af11a5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "hash": "99fd90c931371f761b8d1b1265460e7f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "hash": "07f333d11d639a1ac8457b95be96147e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "hash": "da9f7a464b831130a717e80377bf99d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "hash": "7553fd67cfc34993a2cbb1e2b56021b7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "hash": "a6233596487e99f0490dd1cd028b2b6f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "hash": "58f525b6ace4ddfe65c9e45799383f31"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "hash": "fafab4ea7d0c1dc06149deaf9d269a85"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "hash": "0b279dcbda1933bafe0bd7d322e2000f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\logger\\sqflite_logger.dart", "hash": "67d4b61f3e0a5ab742adbe76b7ec14a8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "hash": "7fc728538b4621c4c988f1f4eb76a951"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "hash": "f73ee5757904401297c37ce114e2d2a5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "hash": "2695053ea1fa59545adcd8b10cf7664e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "hash": "c6dd0c20e5521905acdd0e209727ec65"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "hash": "fbeb850ec795afee0dea45a73ab1368a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\src\\grapheme_clusters\\table.dart", "hash": "29e1858c5ebc2b4dc6d1528196bfb1b6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "hash": "798aeabf18ac4da97c366886deb0254f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "hash": "89d367efde9afc4b7c990508733651b9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "hash": "494669a07dc9803f9dc8573c081f3084"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "hash": "8a2ad8a7289e2e5bd7fe7149614dc2fc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "hash": "26c5d3b0b3aa11fa45df82b9fe2a0388"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\lib\\src\\utf16.dart", "hash": "07d628617431f09942070c95c65d241f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "hash": "d4e6c628f51f84b669afeb6880e25d3d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "hash": "8ff9154cce54a72addf2f48fe65f266f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "hash": "15fca68e23a9c248049f847c1fb4ecb6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "hash": "d69c889a5ae0efd9db8fc23c11cd6bd3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "hash": "48d033e11d964357c0e0739f71388b23"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "hash": "31f9f6ea321b6198be1604f8e2410b20"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "hash": "863b0fc44e94df789087c6201ce56e2f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "hash": "a5e1196791db734a448e0fd32a63cfcc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "hash": "04777c53e15d3e5a3edcb50908493743"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "hash": "8bfc94f73060d5f1224472caa792b8b8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\test_api-0.7.3\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\async-2.11.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\style\\windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "hash": "bbe955412891750438ad423a96f5ef64"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "hash": "32054e55ef3aedcd41e364b791ab40d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "hash": "f1bc1311609e5f19d7e874122b43d169"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "hash": "870a874a6635a84a96e2ae37a78c0820"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "hash": "e6311d4728a75edc5866998cd9fcfe96"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "hash": "704160d71eb5efcdf2906170d893bcf3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "hash": "db58af9f6da58e4aa822f325502532e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "hash": "1b68a5a0d959cb0874c7ec1d57eee204"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "hash": "8d5d3ccddf53eafd7a3094278afe8b93"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "hash": "d26af0099253cd637ee68a15ec360d59"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\widgets\\song_list_tile.dart", "hash": "6edb496224bb2d8a39bc227d8aaf0463"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "hash": "5a64efcaa7dd1fd5683d9089e469e1f9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "hash": "f205ff9eec124faa7b02e6551b7fd631"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "hash": "8b305b4fe533a3762afc8ae95d4c49a9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "hash": "7caaf84e2dffd3175c4b86ef5f712379"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "hash": "37cd874a940881634437bb35ab857556"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "hash": "801a669a24457def40f962e55c2b418d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "hash": "6a4d5a8b84e71967c08ce18adb19fd9d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "hash": "161b7121f78b81515c68a953d2a1f372"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker-10.0.7\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\pubspec.yaml", "hash": "70880fc3effd70a94dd304d4fce7584c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\providers\\music_provider.dart", "hash": "b0da97a06bdb1290c4425a5fc9c1347e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "hash": "d7223c3719de78eebad967d581c78afd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "hash": "d215e2ab6831449adee2f43a9cf21dde"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\LICENSE", "hash": "fba1d88f08129f8046dd091b8abb5ded"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "hash": "2c5e68dfdd6a069cf613e539e2da5d24"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_service_platform_interface-0.1.3\\LICENSE", "hash": "0f46b6b83484481b72108582bb5a6138"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "hash": "7ecbe394f5a9f436bdbd973a49c8281b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "hash": "63f6f34dbf353dd0dccf02e3579f109e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\string_scanner-1.3.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "hash": "4fb5d90860bfca49f93097e7c8d882ea"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "hash": "397669b16b3f0b3a5786a22fc3c90369"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "hash": "e61baa67949f9568bf0b72430552bba2"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\screens\\player_screen.dart", "hash": "f285f540c8f28b498dd5df51f4f346ca"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "hash": "348756f557453aa6a59d2f754e1839a8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\matcher-0.12.16+1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "hash": "2ed03ac5e94c43f504b49a19f6e49619"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\scheduler.dart", "hash": "3ac176a9235973980af3b75bd0c237ff"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "hash": "dc18942dbbb12b9ef308569bb6dc07de"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "hash": "5c950a52522434ebf9571fde435d4dee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\lib\\src\\android.dart", "hash": "11371fbee1f3cfa5292a3d7b9dc85358"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler-11.4.0\\lib\\permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\lib\\ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "hash": "4830d28e3ee4aa9ba1501a9d7bbee289"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "hash": "ebd1c81fcad683859a7e01887db755e0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "hash": "2011323cb0091888650cba67462f3e9f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "hash": "6a8a899adcd3897a28c6985f0c239886"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "hash": "d74003c764e6c2d209bff3a2ed86e777"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\database.dart", "hash": "58b3ba88bbcf26534262ce680263e08d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "hash": "a5c02581df820933c57314230ed9d33d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "hash": "6554a6b0b984ff6d52d5a1685db23893"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "hash": "7848fb52b88d8e30f0c2e9b6228525bc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "hash": "a78fb79c5ae5aaca3a06db2bd0f1ad74"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "hash": "8a9b22d6d62b5cdc288ea7a7f6b1ffe5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "hash": "a00f7da90c85736a52cd5ff63991ebd7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "hash": "239611e79aa9c12fc4864c4ae7c78d2b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\boolean_selector-2.1.1\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "hash": "670717573525d5e0518a4063c6fd9231"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "hash": "73622a69d8741d566c6f929e31f65a4d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "hash": "8f3a150dad1ebce992fcd7279b087d21"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "hash": "0fc09d347fd6e5149debdbfd5d230450"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "hash": "3368869abc38a760e338f506076f0559"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "hash": "b2074976267af6228ae6b50627b5c9aa"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "hash": "780afec8afee633755faec912310be81"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\json_annotation-4.9.0\\LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "hash": "75b9c1779a7340c6547ca36854566e0d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "hash": "3b68b2d629e5cd3e8b089b459605332c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "hash": "51cbce8ca842608921a9a5ec8cf0981c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "hash": "18939fc6e74f9b391a45ae341fe30128"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "hash": "fbdb2ee0f52113dd3516920d415a60f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "hash": "270df3966ba1802f0cc1a5c3f8c77537"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "hash": "e1886dc8e1edb2fda48b8a8d497ec379"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "hash": "1188e587ec792345163653190819f06d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "hash": "fd9ef2d2e4dae856651e05a2a6ec1f63"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "hash": "1c6b1474b9b0c0c8d119169904f41ce0"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\models\\playlist.dart", "hash": "c613baae0fe40bdc007ef78d673008df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\http_parser-4.1.2\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\flutter_build\\e7e739fb4ee519c6b05a0c88c2d93f1a\\app.dill", "hash": "945a347362d34c09a77356f917775073"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "hash": "d355a4d910ba42786220a7ac724dce8c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "hash": "10b20e4a220ff81322487cec3b397008"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "hash": "549517ea589b74d9e07d4495e26e0bbf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "hash": "9bf9f497e71504dab15bb024be56ce1c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\stack_trace-1.12.0\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "hash": "a3247e847626b86604378426b8b05c5d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "hash": "31536de0cf8c179f5d3a157a383853ee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "hash": "c53090ab624be42bf2355926a903a034"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "hash": "a04e00aee9f371d41cf413e644823901"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "hash": "f09bfe5349c4d6ae49a5d093ce9d8b58"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "hash": "97795bb93646fd12a7f9cdc6982494ad"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "hash": "ebf34631057a314478f9be390f80fda7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\src\\grapheme_clusters\\breaks.dart", "hash": "359388897ae53df8791213c31ef05fe6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "hash": "1d552d8d41a00a356003dbb21453e904"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "hash": "81cb6391e0df23208c07fb243f46af37"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "hash": "15dda7204de4db77a10b27a8d20902df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "hash": "db008d9d847b5c30b4ecfe80ec7dcc14"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\platform\\platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "hash": "9d2eee67f6cd2ef4617277bd058ef797"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "hash": "dc96d8fe58eb40b6dd7141530390247f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "hash": "ef059aa370903d40abcfbcb845bb6532"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "hash": "f642cf1205a554270486050c78b16e37"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "hash": "f077efd46130f69fa6752d282168e612"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "hash": "bbe07eeca969d22bbbba71a2eaa2fffe"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "hash": "5dd8400633f5e9e7f1f8bf48e7dffca8"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "hash": "459fbcfc6b1240685ec3f59fea18ed9a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\screens\\music_library_screen.dart", "hash": "8a42b7986ad9dfb6e4235a86b9e91a8e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "hash": "79a40e88699dcb8faa97ae847f869f00"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "hash": "d8a93c8e75a417cae210d942f256ca5e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "hash": "c125a915dadee85fe8fdae8d0ae54410"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "hash": "b9512ce445b44934defc0f52ac54e359"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "hash": "99d0ee83080f8e15e59986da0c527852"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "hash": "7ae5ac95d2335d3e6d414e4eb7591228"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "hash": "c99b3887957e017aa0867d9a56bb9608"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_android-12.1.0\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\cupertino.dart", "hash": "f7f1054eac95be1f773acf9de3f8fa90"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "hash": "b999d3540cc7ab70522bc138712bc2f1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "hash": "06fc4cae1d1668906f5e4b931b70a5f7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\factory.dart", "hash": "90c345ec396d7f892f524ce659aa9b5b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "hash": "23f02b519da70fc68bc0770027370b31"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "hash": "2efbb41d7877d10aac9d091f58ccd7b9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "hash": "7c7512d53c9dce12c960425241dbaec4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "hash": "8910e1a56d71bf34bcd3e5cd8786f16a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "hash": "52fcb3f4a2ab54da9f5308e559bf8c1a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "hash": "ad7a0d8554e1cabf6530487c93e0dcc1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\foundation.dart", "hash": "bbf382866dfe5839f5fd524e1bcfba6b"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "hash": "7344cf3ca556fab3e221f40653945112"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "hash": "918c1513952cacf39a14339a71ff4ea1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "hash": "48ba673f0e14a195d177e35cbcfc679d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "hash": "999e8d6e7a56dffe8e1edab86cddf90d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "hash": "a233f949548bc7e1bc2988b8c264fc9c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "hash": "26eeff5813436e5e735ebd1ed8e94448"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "hash": "51232f90052d5aeb0e8565b3a4bd4305"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\lints-5.1.1\\LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "hash": "643b84b4297b7e330dfa23ea461585ef"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "hash": "8c2cb59c4b074ec3ba427024031056e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "hash": "a72988c783819c1affb50f71cc404339"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "hash": "a57609688ba407442c358d4e1294739e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "hash": "ebf283bb4286732edeb1e354845e0c88"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "hash": "53e368901ef47a75e50412f3e215fa3a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "hash": "74578366d8f8de03d30f48d6296d36a0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_android-2.4.0\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "hash": "87b94f5cab50bff28b574c801811b29f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "hash": "d8a7b96e6465831f5cf6de56ccddf6b4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "hash": "ac4298690945913eebe3982ab06e52ce"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "hash": "4aa4347adeb3288717eb12cdc500ca9c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "hash": "9e98e6b849c9f74c7a5fe26ea85a1f01"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "hash": "1717d85af1439d56bdfc2dabe531b8a6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "hash": "9661eb871d12fbc5d80f90c86f325d96"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "hash": "387236f694dff79733084498c52038ba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "hash": "23aacfec6ae252d6dfd6e205b00c385f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "hash": "cf58cb45948b8d1ff8d157f342d895b2"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\services\\file_scanner_service.dart", "hash": "05bbe17df63cd6261ac9c2d048ab3550"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "hash": "2f1331309bb296144b3445f855f5ef7f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "hash": "f71282d25b5792dcbb6d9917076d0f30"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "hash": "6e7b0efc63ef2536d0ce9fdb301e1eb7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "hash": "c73badf5156e45c15d8737e32844597a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "hash": "b29b21a1f3c62c0fa77ded7b6d53fe4d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "hash": "836cd409d91948bae18eaabea344f753"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\widgets.dart", "hash": "85e7d38152211cb1c7ff328f96691f59"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\http-1.4.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "hash": "38a87ff489a47bc024400dc863be326d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "hash": "4bc258e047bde60b5472dd5db45738e3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "hash": "93fe233cb281b8cab8872397da5e596c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\combined_wrappers\\combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "hash": "4fe1ba752f6c3911ab5173a76c415893"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "hash": "c479d980bae3f60e0ff425651385d6c8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "hash": "9c22a82a905c0c4467f302f10d337c1e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "hash": "c692323b8d9e3ed3c4c134ba07ab94e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\style\\url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "hash": "2b56d2e2d43e189c29cdf7b550bb8cab"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "hash": "41e81b82c8faeaa33edebb1e8b6375e9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "hash": "97f1af611955957117513d5e9ecf31ce"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "hash": "eb5a28f2fe16fc3540983a939e85e177"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "hash": "5749fd2b60f7c6ae600ed6bfd4679756"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "hash": "bdf0a9e4668e4ebcad7d9a3f8d3dbeb7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "hash": "43fb67a95ca01df6e30ae173cb8b33b1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "hash": "4b27a964d3400de0eef896a6228bab68"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "hash": "2b896eb9c2c6269a1988162da3e4efa3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "hash": "dcd173e0678899401bff84c3a256bc9c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\mixin\\constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\utils.dart", "hash": "88c24cb06bdc7279bc361d0011de71a8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "hash": "ed51dd3f812191698dc5cb29c84f11d8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\src\\permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "hash": "474f2f3e04989d90275f6b0828e173c3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "hash": "157cc653d91298c7157676cdc31f2f18"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "hash": "6bd7116b39a4290636c006ea8fb0f535"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vm_service-14.3.0\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "hash": "9051680cd2078f92c9c56831272643d5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "hash": "9d9ef34f6520667fdc7d6831ced8cb28"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "hash": "c76aba086f5653df25d6b7698b8d85f0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "hash": "bfdd5f136da67a6a2e572ff7ffa18641"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "hash": "32ab00e06c2e96015944c29272042891"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\characters.dart", "hash": "188d03c92376ce139ce247b0f9b0946e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "hash": "99279951f4a4e11373ee3b14936c7eb3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_apple-9.4.7\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "hash": "ac504413ac252d4facd173131a205b9f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "hash": "1f0bbeac096fa8cb1e547e27d4a6e09c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "hash": "2e0b624dda8810b895e5c36141bd0c43"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\flutter_build\\e7e739fb4ee519c6b05a0c88c2d93f1a\\native_assets.yaml", "hash": "e7fd2fda36f01436b831ca47fe61fec3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "hash": "346f2a019ad1ea1d0d106b406e38e1dd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "hash": "7312c07818673d611233ae34da9012b4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "hash": "8d511fa4a89334dcb252fc8ae9953479"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "hash": "d2b7f722c662ed9c5e1ebf87fc18416e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "hash": "28e75a6163100cda69edad35f4d9202b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "hash": "d372dd7a5afe4ba4e9439fa0e08fba24"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "hash": "1db5346bf6d1d26a2be1e39e36f7afbf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "hash": "968914307d3a5e22499a9d5b76a0cb8c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "hash": "abcdf5b8d942cd3f02cbc60002c824d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "hash": "945a347362d34c09a77356f917775073"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\style\\posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "hash": "7c35678c36fe8de28cfa6384558d8dbf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "hash": "b614d8172098403c683c68aafa3e92e8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\lib\\src\\reentrant_lock.dart", "hash": "f3f1d126f0461776f6230c368eacdc70"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "hash": "9469004432b70b7085c9ce3b4ac94293"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "hash": "c70cac9a1261b64861fb51c0f0519c0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\term_glyph-1.2.1\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "hash": "443f13b86008c5d27f31f673efcc3267"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "hash": "569c61e8718e8fad8eb64bf91adef33b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "hash": "3827646c58ecd33b01c03b13ac7ccbed"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "hash": "de1cf28feb5e5069cae822e8fa02fca5"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\services\\database_service.dart", "hash": "fd63291a4ad0f0c0b542fa290675377e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_service_web-0.1.4\\LICENSE", "hash": "01619ce8caf455c27ca7fccaf212ed05"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "hash": "97ab419c484b0d1eef6feb63b3737cd4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "hash": "472f1e91aee127d81e69f84077ec8d15"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "hash": "40116044a46a9585645a2ea94de07f67"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "hash": "0e50d59ec3a3f8caa2af4583be6f2201"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "hash": "1b8ebe04f94a58f7ac6da2d2be484131"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "hash": "c5416cf7f33c8e055eeaa65a8814446f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "hash": "99d0a7ade68747bdde3fcbe431df6612"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "hash": "5a74670bf67f1bd87eda93d4f03fd38f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "hash": "547c71ec0c50920c3254ec3bfd593585"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "hash": "67cead210557633927b538693d93a8eb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "hash": "6f752b00d684f1287d498ca51f0ce435"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "hash": "5851efca699e1485dd117936a260337b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "hash": "7c562abf5bd2819e159d8b62b633bd68"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "hash": "ab826a8c77d2dce014192c0ecb1374d6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "hash": "3b71578d61beed4e9f7c12adf7176c8c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\native_assets.dart", "hash": "e1e2bfa907107c49241169594268d17d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "hash": "ce3079f7f91b90ac9140f5a1e2840123"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "hash": "9271394c46950516587f111e451e189c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "hash": "ef91dac42b5e5b3a475c28cdd20c72d1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "hash": "b70467cc385d9e6114403574d2f4fc2b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "hash": "6d14fd39dfef5b30d204b9fbf19f95f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\nested-1.0.0\\lib\\nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "hash": "fd538a76c2981f47baaf50b2f8fe91f5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "hash": "33eac25cb4c27b0dcda0a4952c4685d7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio_web-0.4.16\\LICENSE", "hash": "fba1d88f08129f8046dd091b8abb5ded"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\lib\\src\\multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "hash": "d970423c6488cba52d3585e0221e57ba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "hash": "6ca4dc9ebc0188fb83d496476e2072c9"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\screens\\home_screen.dart", "hash": "93aaf8af05eb1c7a6313d9f0d776ce7c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "hash": "214d2ababd129745cd0fd19a8c03473a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "hash": "a61ee31c49a4aaf5f3a98af02f8b7ae7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "hash": "0faa933d622ae839fc4fcf8ce3439ea6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\mixin\\platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "hash": "db628159b4bec9f382987feafac814f2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "hash": "85371fca3ff62a5292c3cde9c2db273e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "hash": "6e464838b7c90601558fafb4828a146f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "hash": "a68436d4e35df7697a2847302a1b78bf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "hash": "c2d786aa36eaa6d2b8ac8b0149099d99"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "hash": "c8c3c692c4418dae54fe1e830cd4378d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "hash": "ae36cfc622f99dc7a17a256be2933b74"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "hash": "12f710e8dbba7c8a644ec36c584093e3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "hash": "dc64ebb08fcf29bdc05884be98af7daf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "hash": "310649eda1830de4f669332f93865282"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "hash": "230a3518091834c1ebaba0eda6ad491e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\file-7.0.1\\LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "hash": "14dabe368f43293be3ef87bee57a7f14"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "hash": "3e12b857d02fdda256ac8acbe87d960e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\meta-1.15.0\\lib\\meta_meta.dart", "hash": "8b83501f9451392bceda63c9281db57d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "hash": "8d1b7aad44c0c6f29e95bb7aacb00704"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "hash": "6c8da04dbf0c874b0079933d8c805cdf"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "hash": "fe6d589e79d080334b661f0861e25191"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "hash": "f0f00e72e2a309327e0e0468887e7315"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "hash": "1ebf6345c59496488173dfbd3f7c3526"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "hash": "67f572754eb93e6d46cea46dd1b81bba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "hash": "28f82224d294690d1c6e1021dc0ebf5d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "hash": "14b41484338436c368d2a0995e9020df"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "hash": "b9266022a984609da99cdc7575845b4c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "hash": "27bef87df3fce59297c7c6aa93738dcd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "hash": "f457eebde02f336ab8da5acbaf3d8528"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "hash": "16f396eab9f772df85f0b0f50018dd7a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "hash": "eabb108dbf5b92eaa3180fae75d1c3f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\lib\\src\\basic_lock.dart", "hash": "1f54b1a8a2e83cb33b70d8106a275698"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "hash": "87ce2c595f6f4415633ebb4f8baf9c1b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "hash": "9199de8ac9b51c5258f1b9bc5945e6d0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\lib\\src\\darwin.dart", "hash": "30f864a2edad9c20e183a80c3b4eef42"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "hash": "6b0bdadf0113f2cf1ea049d096153df0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "hash": "b67c452135251581ea39ca220a3dc326"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "hash": "f2138801d3af6774b0cdb3ff8f966b8c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "hash": "28600f5b0a3d53275c980d0ecaf71e35"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "hash": "e61df3dd752483b76984b44d3d37ab71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "hash": "5c9c56e47c1aeb615f002ba155685a65"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "hash": "2e5d93dfd0e1e6ac7f95e2f609bf2c81"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\plugin_platform_interface-2.1.8\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "hash": "bdbbb8551f60548401d6b10b9dba71dc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "hash": "2938a10e708e941766fa7c750c9c34e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\rendering.dart", "hash": "de159249f2567411616f741dc19a7317"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "hash": "18c03ecb2fea51a8cb9de728de20be92"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "hash": "cda60e978161ad8864827715569a6015"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "hash": "cf4765eda606ec7c93129987a7b9f467"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "hash": "273a0c4c20f7a2b715c34db25c737264"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\combined_wrappers\\combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "hash": "da8ba17f9e25e960c8449fe98007f50e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "hash": "a69934394ea5ba56f5289395adad99d2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "hash": "6864d2aea4893029b4fcf2fc3cc3a091"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "hash": "e70000bf4405dc60eff2da33c096d7ba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "hash": "f5a0ec2d695abc02a7ef60473bdde956"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "hash": "35d774fa98bd7f24fc75df96c3269493"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "024ba044d40e55c1867a7b96d6dc8760"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "hash": "c87e92035314a4d3e52faf886355d0a9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "hash": "b168e7f9a027f1d58d126472929300c6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "hash": "f011a47745fd8bd705d133bd46d00617"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "hash": "74ab9e14501d5549532bc172264d0424"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\utils\\notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "hash": "eb8a87eab443c1122f0b577a5a7d0674"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "hash": "********************************"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "hash": "0eba84d1e22dc75e2435132f46b03e75"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\build_cli_annotations-2.1.0\\LICENSE", "hash": "955c29161ff462f8ee1e1e0ff030a928"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "hash": "cb0e23db8ab17f9c9f2f4a8a766e50f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fixnum-1.1.1\\lib\\src\\int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\sqflite_debug.dart", "hash": "ae7e464ca638c7f5aeb5c9d15eadb1ba"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "hash": "10fd391490d7320415aea296125ca50e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "hash": "fcd2bc3a00d0e7de6c498109ffe95a07"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "hash": "a49ec697c5cef9953e0bd0cbac9d97b8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "hash": "bde138fb87621d2d487d8955f9560b48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\typed_data-1.4.0\\lib\\typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\src\\permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "hash": "f813af5cbad68ca07a6ab1b8959434a5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "hash": "fdbeaa39b664f6fbef06b3e56c317cdc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_platform_interface-2.4.0\\lib\\src\\platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\lib\\path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_rust_bridge-2.7.0\\LICENSE", "hash": "480e9b5af92d888295493a5cc7f2238e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "hash": "2805633525ce8914c6f3cfd32e401bb7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\stream_channel-2.1.2\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "hash": "a2701656bb3160ea810ab576c50cbd65"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_lints-5.0.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "hash": "4cc3ebba7ffe6fa47bc12524c53af303"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\combined_wrappers\\combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\lib\\src\\arena.dart", "hash": "b9bf4c34257ba7cad08d8c7092c46e35"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\src\\dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "hash": "a39036e6e7861dbb8dde953722ccef25"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "hash": "71792db426dbdf014d798add6baf0a84"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\flutter_build\\e7e739fb4ee519c6b05a0c88c2d93f1a\\program.dill", "hash": "945a347362d34c09a77356f917775073"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\web-1.1.1\\LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "hash": "09affe54f14fd2d19a0ef40318d17f8e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "hash": "fe21271614c8485bd9b8aaa6242623d2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\clock-1.1.1\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "hash": "0b8d24a7191ba03b322f83ce1590e2a7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\services.dart", "hash": "1566a1649e9bc2e0f0ecdf869e678a0a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "hash": "7a23f649958f737fcf117d86f635f1e4"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "hash": "693635b5258fe5f1cda720cf224f158c"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\services\\audio_service.dart", "hash": "f5b2fbb4b54d632f6862ce8ee35e7e4e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "hash": "f442ee79a7d1cfe7021ad844e64c6cf3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "hash": "fb4a9b5871529d826c0f3d9e57d836b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\src\\characters.dart", "hash": "21bf6725b1fc374f03ae5b2cb46bd95b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "hash": "7d97806d13ca94b081b431f4d3017d27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "hash": "0bc32d9519ad188655722c1b5df8e896"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "hash": "bdd3a31817dfc052c506f3a7e2556fcb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "hash": "f15f34717397d60152d5d34a33b75a88"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\main.dart", "hash": "009026bc92f2000239aabc33c4208e81"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "hash": "2d08e01197ac81991b91471f886a77af"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "hash": "1f699fda56a8ea482e6bb4911849a5cb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "hash": "e1c116ad0a584a34aed67c041ea64d9a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "hash": "cd0c4ddac2c7f994effd14ac77d18226"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\utils\\utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "hash": "225c715276cd415e4228eaef6905c0c7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\lib\\path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "hash": "793840286fbad13c5aad3a7cb6c9fd24"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "hash": "d132c1fc844a3300b5faf530714f7d30"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\widgets\\mini_player.dart", "hash": "fbc48d4a70e56e408a0f3c7646b6826c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "hash": "11633fb39bbd1a95bbcae50aeb2f657b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "hash": "a09abbaf7209e37fc309c9852e7c4396"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.17\\lib\\messages.g.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "hash": "d34e181f5286390ce6d10a7bf6506bc3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "hash": "c1d35e1537398f54115d9f56c2bc922f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\internal\\engine.version", "hash": "627af03cf05ac5f79afff5b2b54d6e6b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "hash": "66f2e40c42e8db6b6ea0e1235270515f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\mixin\\factory.dart", "hash": "5b48bba3dfcba0ea1ad63f36aa8bf66c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "hash": "15e1a561d8ddcea33d3678576c164b6b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart", "hash": "03290a77de60d1938a5419ca28e0c9a3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\sqflite.dart", "hash": "badfcf916ad8ce9a472579d404da90e7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "hash": "d540f5a7e71095a9b49cd7ef8ba54bb4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "hash": "f3a2a87e2b666b138fed19428dbc28f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "hash": "64bef592badb8d57cb09c05fb0c76cc5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "hash": "44e3b08900a8e89a62092676f2245f09"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "hash": "d732fc60bfabd0e0cc57820d422d13f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "hash": "e9b8b2f72c1b05a3f6d26b4ec3cd7c1d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "hash": "b640a31377f9378484714523f25be099"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "hash": "d25bd308a31a0e9435238b8bd8f274e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "hash": "64288b7c30e61bbfe84d9baf66f08c8c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "hash": "b7ff96a27c667a22ccfdace98c6ca7fc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "hash": "e4a800bc05d33cd889fb85b8d5df24bb"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "hash": "64a7812bfa1a336d67245e7ca06fa364"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "hash": "d67821b66a2c89caa5ce02a5a584d02f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "hash": "f0c59c9b18125a83e52afaba0ac60f71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "hash": "74f4c0a620b1c4ad63da4929145d9f42"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "hash": "ff388aea80ef0f4e3ab47f1f06ab6c31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "hash": "d7efb3cc784a7c2526ef94b353ee0845"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "hash": "ba5e6431793ca2418b3beb3a89f581bb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "hash": "6815dea119b327bd3ce8756a1e14773a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "hash": "33d98f0a91d79a14d7f56d8c9c06ce0c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "hash": "9fe869d7b38ad8c9b2b82b8755631f88"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path-1.9.0\\lib\\src\\style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "hash": "e6fe3e33d2cfaddd77eea1d11e14243f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "hash": "ad9758d9e1841409e9404d74fc3bd9df"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "hash": "5b488752ca5b7d6ba586a6a4d062cd36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\leak_tracker_testing-3.0.1\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "hash": "214bfdcb3029a0f80f320b76fa9c0a5d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "hash": "ef0cb76da4afba716098e266cf6e2d20"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "hash": "454f11813e43a193cf6fa422fc834104"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\streams\\fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "hash": "446e0ae8964c15bc5518317cbe83aa31"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "hash": "95e57e7ca0c3a96d640d41e198b0d8d7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "hash": "6b87886fa93ca01768c7db20d34bb3b0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "hash": "b98145bd156a782be220cb3f652ba0a4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "hash": "f4724af4e9b7b1000bae55271c633234"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "hash": "90f2bfd8a277caf2dfdda31d1db65bf6"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "hash": "0a6c208a7cc6ff0f52ca7ff3f7d1e40c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "hash": "130bdeb556d4cebb55205def587a6a81"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "hash": "7cd7e722df86724dfebbd5ec0ada9097"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "hash": "ed6111dffc74b8cd4cf2ad7195512349"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\src\\grapheme_clusters\\constants.dart", "hash": "9f9b79f577d9fdf4f20c17a26a2f1d57"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "hash": "e5e333993a5cfec88d0277a09718323a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "hash": "ec0a4eb8a6e7be02a35e2d2b01668da0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\subjects\\replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audio_session-0.1.25\\lib\\src\\util.dart", "hash": "9eb1f8ab2bfdd218f9e8c25acaea4233"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "hash": "a3b337836937c13f49a39743b2e9dc73"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\synchronized-3.3.0+3\\lib\\synchronized.dart", "hash": "4b2f30555392a9ce4849932c9ce4063c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "hash": "47e70daf42f41d4119ad4bc07b923928"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\args-2.7.0\\LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "hash": "ae9dbf31746490c96903a18b869aadd1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\src\\formatters\\Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "hash": "e6dc8fb43fcd62e902975e9fcc0aaf5a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "hash": "06c8940894956210e1d9312848a9b556"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "hash": "07be594331898d1cdfa3d593809e439f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\ffi-2.1.3\\lib\\src\\utf8.dart", "hash": "3b21907d68a2e99afa8e4103f6a72f78"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "hash": "a03d0dea6d2e759ed26f512dc97a03c2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\exception.dart", "hash": "5ee7523f60ca432b7896fbc746ea64b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "hash": "645dbc586e5292c99cd46991412d2e89"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "hash": "5588e04d2462fa3c03dc939826b0c8d1"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "hash": "ecea94a67645c8c6f2f2c2befdba5797"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "hash": "35bc3e2b8d3bd9a441045ed5f50a2dc1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio_platform_interface-4.5.0\\LICENSE", "hash": "fba1d88f08129f8046dd091b8abb5ded"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "hash": "e7235d21d265d455e125792adc7c8eaa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sprintf-7.0.0\\lib\\sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "hash": "61a7c8cb019d7b4b133dc84382de854c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\just_audio_platform_interface-4.5.0\\lib\\just_audio_platform_interface.dart", "hash": "6a49b86842ace7ce1cc5a6874b409ced"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\material.dart", "hash": "e0fc58cbe26c8afbae96f489212595fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "hash": "40bcde54f6fdfd7dfc4e68b050122987"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "hash": "07d3dd77eba249c98ad68e03038206db"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\src\\permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "hash": "ec4da34caab07694f3c251c780020e96"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\collection-1.19.0\\lib\\src\\functions.dart", "hash": "ff39615e1c6340c049a00342dd7ffec5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\LICENSE", "hash": "ca58010597a5732e6aa48c6517ab7daf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "hash": "f2c0c439fa7d9e5a3361fb1d45f529e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_platform_interface-4.3.0\\lib\\src\\method_channel\\method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "hash": "23c281553020294b0d0b362055cd6532"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "hash": "8fb77ea0e2407a18955da641a64f1c14"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "hash": "59ce9f77f19f81edc23711c654702038"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "hash": "7e14c5d8a84bd21772dcc0eed46721d8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "hash": "92f15623a771bdb91623beadc6ad1a4d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "hash": "41d7e4aca9095aa0ffa6d18fd994af07"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "hash": "89279f0a997fc74d790892ecfb894eb8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "hash": "082279eddd3ecbf4cf4cd9e5b79cc91f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "hash": "14324f4186cbdd16f325bf1cf192a00a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "hash": "b79bd48d58944618e0ef1f4f5d204a9c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\src\\extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\characters-1.3.0\\lib\\src\\characters_impl.dart", "hash": "3bb0652e163327c58784ce2a2b882a7c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "hash": "fc18de161858c5173701202cea00559b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "hash": "2b2385e013688dc5ccafac580f8f6999"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite-2.4.1\\lib\\sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\sqflite_database_factory.dart", "hash": "ac8623f54bf17085dd3500961389a642"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "hash": "4e68fd3411373099a0ffa869d0eeb41e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\crypto-3.0.6\\lib\\src\\sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "hash": "1b60e56f0678efe687c81d0409e2bbc8"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "hash": "a41dfa7938a3c34c93159dea0a6c658f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "hash": "ace1af10f05915f711ae44372e6862fa"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "hash": "8578e11f0e909ced71175ae8aeaee19d"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "hash": "7eee695ba96e5afa80abfaf59973617a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "hash": "22f86c3c4f9ea753ec9c891e77b29b84"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "hash": "72d4b7c6ab9c5614166b19ce28c79869"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "hash": "12d23fd01b29a6ad40525e4bd03a5fe0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\provider-6.1.5\\lib\\src\\devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "hash": "7b62eb0ab2c5eebb74cb9830db58dbf2"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "hash": "f80d7815d221dbd800f96fd9c8b86d1a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "hash": "4932099c2a6b8ae8b74ede9a615b447c"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "hash": "8ed63a11f5c6520ad3ff2f3c9b1c7929"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\uuid-4.5.1\\lib\\enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "hash": "bf7acebb230ddf490625b2cf8e743e2b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "hash": "15348b8318077889ef48af7fd360e3d6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\permission_handler_html-0.1.3+5\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "hash": "fdb73cd1fa108335e45a7bef127f5745"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "hash": "1facdc61a637ab9b72f3e3e07b5ac0d4"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "hash": "7cc7aeddf9c2fc7af65f7db5dd3e5089"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "hash": "97248f2ca49fd5ec6acc129f1eaed40f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\sqflite_common-2.5.4+6\\lib\\src\\constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "hash": "080021219b8d99a3f45f177f7c011689"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "hash": "cc1338d16fcc0c099b17fe559a1af4e7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "hash": "e00d761729ce91e58bb9edd42608d6df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\lib\\screens\\favorites_screen.dart", "hash": "03a406e18e5760cc1e5ca6dffcf30d0d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\flutter_cache_manager-3.4.1\\LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "hash": "b4317068d9d7b3f0eb870c72d09ddb5e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "hash": "193d1801b76c52075287857a01d3fe83"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "hash": "617a7299a6876bd2896d2aa0c2f845ce"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\semantics.dart", "hash": "a9d4e4b3f6357c540f77101737a25e4e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "hash": "e6a6aeeef7b8f50db0595e907bfc2f22"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "hash": "783d79a076dca8f6090609673e94f1d9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "hash": "395feb1e3c41a7e8086f80d26775c95f"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "hash": "1dedcfc721ff198bfddbf5e90e14769b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "hash": "8c8e376b1f1907139b4cc2a77ec40ff9"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "hash": "5986a9610f98058f977fb497d397d198"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "hash": "ef8e59ac8fd1fb4a9b8ba40093583ad3"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "hash": "90bb26568ff55a583cbea386ce423560"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "hash": "cc4964be8543fc1ef3be2e36fa7dc449"}, {"path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\.dart_tool\\package_config_subset", "hash": "2c9680efd10b3b795e28c09651824c48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "hash": "8f27bfaeb511bea787cbdcffb888128e"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "hash": "61c1cef76185f009f477c3d9dd49c4ba"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\source_span-1.10.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.5\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "hash": "9951dd026c1ec46f9940016f4a823b40"}, {"path": "D:\\QQ\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "hash": "0e0901d74e3b66a8d7395afece8eafaa"}]}