import 'dart:async';
import 'package:just_audio/just_audio.dart';
import '../models/song.dart';

/// 音频播放服务类 - 管理音乐播放功能
class AudioPlayerService {
  static final AudioPlayerService _instance = AudioPlayerService._internal();
  factory AudioPlayerService() => _instance;
  AudioPlayerService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // 播放状态流控制器
  final StreamController<bool> _isPlayingController = StreamController<bool>.broadcast();
  final StreamController<Song?> _currentSongController = StreamController<Song?>.broadcast();
  final StreamController<Duration> _positionController = StreamController<Duration>.broadcast();
  final StreamController<Duration?> _durationController = StreamController<Duration?>.broadcast();
  final StreamController<List<Song>> _playlistController = StreamController<List<Song>>.broadcast();
  final StreamController<int> _currentIndexController = StreamController<int>.broadcast();
  final StreamController<LoopMode> _loopModeController = StreamController<LoopMode>.broadcast();
  final StreamController<bool> _shuffleModeController = StreamController<bool>.broadcast();

  // 当前播放状态
  List<Song> _currentPlaylist = [];
  int _currentIndex = 0;
  Song? _currentSong;
  bool _isShuffleEnabled = false;
  LoopMode _loopMode = LoopMode.off;

  // 公开的流
  Stream<bool> get isPlayingStream => _isPlayingController.stream;
  Stream<Song?> get currentSongStream => _currentSongController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration?> get durationStream => _durationController.stream;
  Stream<List<Song>> get playlistStream => _playlistController.stream;
  Stream<int> get currentIndexStream => _currentIndexController.stream;
  Stream<LoopMode> get loopModeStream => _loopModeController.stream;
  Stream<bool> get shuffleModeStream => _shuffleModeController.stream;

  // Getters
  bool get isPlaying => _audioPlayer.playing;
  Song? get currentSong => _currentSong;
  List<Song> get currentPlaylist => _currentPlaylist;
  int get currentIndex => _currentIndex;
  Duration get currentPosition => _audioPlayer.position;
  Duration? get currentDuration => _audioPlayer.duration;
  bool get isShuffleEnabled => _isShuffleEnabled;
  LoopMode get loopMode => _loopMode;

  /// 初始化音频服务
  Future<void> initialize() async {
    // 监听播放状态变化
    _audioPlayer.playingStream.listen((playing) {
      _isPlayingController.add(playing);
    });

    // 监听播放位置变化
    _audioPlayer.positionStream.listen((position) {
      _positionController.add(position);
    });

    // 监听音频时长变化
    _audioPlayer.durationStream.listen((duration) {
      _durationController.add(duration);
    });

    // 监听播放完成事件
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        _onSongCompleted();
      }
    });

    // 设置音频会话
    await _audioPlayer.setAudioSource(
      ConcatenatingAudioSource(children: []),
    );
  }

  /// 播放歌曲列表
  Future<void> playPlaylist(List<Song> songs, {int startIndex = 0}) async {
    if (songs.isEmpty) return;

    _currentPlaylist = List.from(songs);
    _currentIndex = startIndex.clamp(0, songs.length - 1);
    
    // 创建音频源列表
    final audioSources = songs.map((song) => 
      AudioSource.file(song.filePath)).toList();
    
    final concatenatingAudioSource = ConcatenatingAudioSource(
      children: audioSources,
    );

    await _audioPlayer.setAudioSource(concatenatingAudioSource);
    await _audioPlayer.seek(Duration.zero, index: _currentIndex);
    
    _currentSong = songs[_currentIndex];
    
    // 发送状态更新
    _playlistController.add(_currentPlaylist);
    _currentIndexController.add(_currentIndex);
    _currentSongController.add(_currentSong);
    
    await play();
  }

  /// 播放单首歌曲
  Future<void> playSong(Song song) async {
    await playPlaylist([song], startIndex: 0);
  }

  /// 播放
  Future<void> play() async {
    await _audioPlayer.play();
  }

  /// 暂停
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  /// 停止
  Future<void> stop() async {
    await _audioPlayer.stop();
  }

  /// 播放/暂停切换
  Future<void> togglePlayPause() async {
    if (isPlaying) {
      await pause();
    } else {
      await play();
    }
  }

  /// 下一首
  Future<void> skipToNext() async {
    if (_currentPlaylist.isEmpty) return;

    if (_isShuffleEnabled) {
      // 随机播放下一首
      _currentIndex = _getRandomIndex();
    } else {
      // 顺序播放下一首
      _currentIndex = (_currentIndex + 1) % _currentPlaylist.length;
    }

    await _seekToIndex(_currentIndex);
  }

  /// 上一首
  Future<void> skipToPrevious() async {
    if (_currentPlaylist.isEmpty) return;

    if (_isShuffleEnabled) {
      // 随机播放上一首
      _currentIndex = _getRandomIndex();
    } else {
      // 顺序播放上一首
      _currentIndex = (_currentIndex - 1 + _currentPlaylist.length) % _currentPlaylist.length;
    }

    await _seekToIndex(_currentIndex);
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// 跳转到指定歌曲
  Future<void> seekToIndex(int index) async {
    if (index < 0 || index >= _currentPlaylist.length) return;
    _currentIndex = index;
    await _seekToIndex(index);
  }

  /// 内部跳转到指定索引
  Future<void> _seekToIndex(int index) async {
    await _audioPlayer.seek(Duration.zero, index: index);
    _currentSong = _currentPlaylist[index];
    _currentIndexController.add(_currentIndex);
    _currentSongController.add(_currentSong);
  }

  /// 设置循环模式
  Future<void> setLoopMode(LoopMode mode) async {
    _loopMode = mode;
    await _audioPlayer.setLoopMode(mode);
    _loopModeController.add(_loopMode);
  }

  /// 切换循环模式
  Future<void> toggleLoopMode() async {
    switch (_loopMode) {
      case LoopMode.off:
        await setLoopMode(LoopMode.all);
        break;
      case LoopMode.all:
        await setLoopMode(LoopMode.one);
        break;
      case LoopMode.one:
        await setLoopMode(LoopMode.off);
        break;
    }
  }

  /// 设置随机播放
  Future<void> setShuffleMode(bool enabled) async {
    _isShuffleEnabled = enabled;
    await _audioPlayer.setShuffleModeEnabled(enabled);
    _shuffleModeController.add(_isShuffleEnabled);
  }

  /// 切换随机播放
  Future<void> toggleShuffleMode() async {
    await setShuffleMode(!_isShuffleEnabled);
  }

  /// 获取随机索引
  int _getRandomIndex() {
    if (_currentPlaylist.length <= 1) return 0;
    int randomIndex;
    do {
      randomIndex = DateTime.now().millisecondsSinceEpoch % _currentPlaylist.length;
    } while (randomIndex == _currentIndex);
    return randomIndex;
  }

  /// 歌曲播放完成处理
  void _onSongCompleted() {
    switch (_loopMode) {
      case LoopMode.off:
        if (_currentIndex < _currentPlaylist.length - 1) {
          skipToNext();
        } else {
          stop();
        }
        break;
      case LoopMode.all:
        skipToNext();
        break;
      case LoopMode.one:
        // 单曲循环，just_audio会自动处理
        break;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _isPlayingController.close();
    await _currentSongController.close();
    await _positionController.close();
    await _durationController.close();
    await _playlistController.close();
    await _currentIndexController.close();
    await _loopModeController.close();
    await _shuffleModeController.close();
  }
}
