import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/music_provider.dart';

/// 迷你播放器组件
class MiniPlayer extends StatelessWidget {
  const MiniPlayer({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<MusicProvider>(
      builder: (context, musicProvider, child) {
        final currentSong = musicProvider.currentSong;
        if (currentSong == null) {
          return const SizedBox.shrink();
        }

        final theme = Theme.of(context);
        final colorScheme = theme.colorScheme;

        return Container(
          height: 72,
          decoration: BoxDecoration(
            color: colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: colorScheme.outline.withOpacity(0.2),
                width: 1,
              ),
            ),
          ),
          child: Column(
            children: [
              // 进度条
              _buildProgressBar(musicProvider, colorScheme),
              // 播放器控件
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      // 歌曲信息
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              currentSong.title,
                              style: theme.textTheme.titleSmall,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              currentSong.artist,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface.withOpacity(0.7),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      // 播放控制按钮
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.skip_previous),
                            onPressed: musicProvider.skipToPrevious,
                          ),
                          IconButton(
                            icon: Icon(
                              musicProvider.isPlaying
                                  ? Icons.pause
                                  : Icons.play_arrow,
                            ),
                            onPressed: musicProvider.togglePlayPause,
                          ),
                          IconButton(
                            icon: const Icon(Icons.skip_next),
                            onPressed: musicProvider.skipToNext,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建进度条
  Widget _buildProgressBar(MusicProvider musicProvider, ColorScheme colorScheme) {
    final duration = musicProvider.currentDuration;
    final position = musicProvider.currentPosition;

    if (duration == null || duration.inMilliseconds == 0) {
      return LinearProgressIndicator(
        value: 0,
        backgroundColor: colorScheme.surfaceVariant,
        valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
      );
    }

    final progress = position.inMilliseconds / duration.inMilliseconds;

    return LinearProgressIndicator(
      value: progress.clamp(0.0, 1.0),
      backgroundColor: colorScheme.surfaceVariant,
      valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
    );
  }
}
