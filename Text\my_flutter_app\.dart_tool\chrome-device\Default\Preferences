{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_info": [{"access_point": 17, "account_id": "00034001603E8EC3", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "90f071076d319147", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "扬华", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "刘", "edge_account_location": "CN", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "00034001603E8EC3", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "00034001603E8EC3", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"available_dark_theme_options": "All", "bookmarks_last_used_time": "*****************", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": **********, "********-9c75-4e8b-89fd-ccc06faa85ad": **********, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": **********}}, "_gaming_assist_": {"order": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": **********, "523b5ef3-0b10-4154-8b62-10b2ebd00921": **********, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": -1610612741, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": -1073741830, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 536870911, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": -536870919, "96defd79-4015-4a32-bd09-794ff72183ef": 2147483644}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0}}, "edge_sidebar_visibility_debug": {"order_list": ["搜索"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "搜索", "pos": "0"}}}, "edge_split_window_last_used_time": "13363778934672548", "editor_proofing_languages": {"en": {"Grammar": false, "Spelling": false}, "en-GB": {"Grammar": false, "Spelling": false}, "en-US": {"Grammar": false, "Spelling": false}, "zh-CN": {"Grammar": true}}, "enable_spellchecking": false, "enable_text_prediction_v2": true, "has_seen_welcome_page": false, "history_thumbnail_enabled_notice_shown": true, "hub_app_preferences": {"439642fc-998d-4a64-8bb6-940ecaf6b60b": {"auto_show": {"enabled": true}}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"all_scenarios": {"auto_open": {"enabled": false}}, "auto_show": {"enabled": false}}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"auto_show": {"enabled": true}}, "_game_assist_": {"user_generated_index": ["********-9c75-4e8b-89fd-ccc06faa85ad", "4a4878b3-89d5-4dab-8196-4b88da4a3a76"]}, "b7a8e9f2-6b0d-4c5b-ae7d-8a6e1f2c7a6f": {"all_scenarios": {"auto_open": {"enabled": true}}, "c8ebbf64-e3b8-41d9-925f-4e3754151ff7": {"auto_show": {"enabled": true}}}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"auto_show": {"enabled": true}}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"auto_show": {"enabled": true}}, "default_on_apps_cleanup_state": 1, "game_assist_apps_initialized": true, "user_generated": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": {"device_emulation": "none", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/twitch.tv.png", "id": "4a4878b3-89d5-4dab-8196-4b88da4a3a76", "name": "Twitch", "navigable": false, "notificationsEnabled": true, "preferred_side_pane_width": 560, "url": "https://www.twitch.tv/"}, "********-9c75-4e8b-89fd-ccc06faa85ad": {"device_emulation": "none", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/discord.com.png", "id": "********-9c75-4e8b-89fd-ccc06faa85ad", "name": "Discord", "navigable": false, "notificationsEnabled": true, "preferred_side_pane_width": 560, "url": "https://discord.com/"}}}, "hub_app_usage_preferences": {"CleanupCounts": 1, "OpenFirstTime": 1693912310, "cd4688a9-e888-48ea-ad81-76193d56b1be": 3}, "hub_cleanup_context": {"cleanup_last_time_v3": 1724918589.791297, "show_days": "10100000001000100000010000010111", "sidebar_show_last_time": 3711850}, "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_edge_split_window_toolbar_button": false, "show_hub_app_in_sidebar_buttons": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "_game_assist_": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": 2, "********-9c75-4e8b-89fd-ccc06faa85ad": 2, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 2}, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "d304175e-bbd4-4b66-8839-9627e56f391f": 3, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3}, "show_hub_apps_tower_pinned": false, "show_toolbar_collections_button": false, "time_of_last_normal_window_close": "13396470792599165", "toolbar_browser_essentials_button_pinned": false, "underside_chat_bing_signed_in_status": false, "window_placement": {"bottom": 1010, "left": 10, "maximized": false, "right": 1060, "top": 10, "work_area_bottom": 1019, "work_area_left": 0, "work_area_right": 1707, "work_area_top": 0}}, "browser_content_container_height": 911, "browser_content_container_width": 1036, "browser_content_container_x": 0, "browser_content_container_y": 81, "browser_essentials": {"last_used_time": "13380462384177617", "show_hub_fre": false, "show_safety_fre": false}, "collections": {"prism_collection": {"enroll": {"rule_version": 1, "state": 2}}, "prism_collections": {"enabled": 0, "migration": {"accepted": true}, "policy": {"cached": 2}}}, "commerce_daily_metrics_last_update_time": "13396470787608109", "continuous_migration": {"equal_opt_out_users_data": {"backfilled": true}}, "countryid_at_install": 18507, "credentials_enable_breachdetection": true, "custom_links": {"list": []}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": ""}, "edge": {"account_type": 1, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"last_gaia_id": "00034001603E8EC3", "signin_scoped_device_id": "bbb35cbf-8dc5-4f73-8172-5d6aefe400ac"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}", "storage": {"state": "{\"app_folder_path\":\"\",\"container_id\":\"\",\"drive_id\":\"\",\"prefs_item_id\":\"\",\"storage_endpoint\":\"\",\"version\":3}"}}}, "edge_cloud_messaging": {"cached_target_token": {"cv": "995024769667575040", "target_token": "LJv+WIy37gnOZAq+rboqCA==$81j9pJ+Q9tZiJhgGA3f+QQjJnuKxaaHLCeuTRHcDyNOmIVTdf/fryK/z6xTQpcVCSIUdKgteLWBL3okbd7gnSq2g3LHH5mBmyIEt63UXPanIM0q1X5QzUrxo1zFN2MS6Ty4zpnr9Ic6rc1sAohxdwdrIb0+sVokuPKGqpU2tc2lWyqUAmE4stV863fgfIaKR", "time": "13396470788192213"}}, "edge_rewards": {"cache_data": "CAEQygYYAEoCY24=", "hva_promotions": [], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "奖励在加入挑战后 7 天内对 1 人/帐户有效。", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "开始", "edgebar_title": "欢迎使用由 Microsoft Edge 提供支持的搜索栏！在此处搜索 3 天，即可获取免费礼品卡。", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "开始", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ZHCN_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13397075587385472"}, "edge_wallet": {"passwords": {"latest_password_management_count": {"2025-07-07": 1, "2025-07-08": 1}, "latest_password_usage_count": {"2025-07-08": 1}, "password_lost_report_date": "13396422192046518"}, "trigger_funnel": {"records": []}}, "enterprise_profile_guid": "1922841b-db94-4192-9082-04161e4e02cb", "extension": {"installed_extension_count": 9}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.3351.65", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": []}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 138}, "google": {"services": {"consented_to_sync": true, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-07-08T17:53:07.733Z", "value": "17"}}}}, "history": {"thumbnail_visibility": true, "thumbnail_visibility_per_usage": true}, "import_items_failure_state": {"reimport": {"ie_react": 62436}}, "instrumentation": {"ntp": {"layout_mode": "extensions::NtpSettingsPrivateSetPrefFunction::Run;1", "news_feed_display": "extensions::NtpSettingsPrivateSetPrefFunction::Run;always"}}, "intl": {"selected_languages": "zh-CN,en,en-GB,en-US"}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "1s2NYWSVSt1SLoH+gb6BlEJn9orOAb9tLrzhpIKJVV6M3TkF6KQseHRAH8qpUt6L9IC22c1P080PH8RjXwcXMw=="}, "muid": {"last_sync": "13396470787607355", "values_seen": ["15415D7399DB640D02A44B5098986588"]}, "ntp": {"background_image_type": "imageAndVideo", "feed_engagement_time": "13396467091349159", "hide_default_top_sites": false, "layout_mode": 1, "news_feed_display": "always", "next_site_suggestions_available": false, "num_personal_suggestions": 1, "quick_links_options": 1, "record_user_choices": [{"setting": "tscollapsed", "source": "tscollapsed_to_off", "timestamp": 1694788694224.0, "value": 0}, {"setting": "breaking_news_dismissed", "source": "ntp", "timestamp": 1751996397095.0, "value": {}}, {"setting": "is_ruby_page", "source": "ntp", "timestamp": 1751993490443.0, "value": "0"}], "show_greeting": true}, "ntp_home_button_address_bar_partner_code": "CNNDDB", "ntp_home_button_partner_code": "CNNDDB", "ntp_partner_code": "CNNDDB", "ntp_startup_address_bar_partner_code": "CNNDDB", "ntp_startup_partner_code": "EdgeStart", "nurturing": {"time_of_last_sync_consent_view": "13396470787750049"}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": true}, "personalization_data_consent": {"how_set": 15, "personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0, "when_set": "13343793254924395"}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13396470788481359", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:56197,*": {"expiration": "13404246792595868", "last_modified": "13396470792595875", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:56197,*": {"last_modified": "13396470788482678", "setting": {"lastEngagementTime": 1.3396470788482666e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.3351.65", "creation_time": "13396470787344066", "edge_crash_exit_count": 0, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_passwords_more_menu_label_shown": true, "edge_profile_id": "f6ebf7d3-5972-48d9-ae48-da236fcef594", "edge_user_with_non_zero_passwords": true, "exit_type": "Normal", "hard_yes_password_monitor_consent": true, "has_seen_signin_fre": false, "is_relative_to_aad": false, "last_engagement_time": "13396470788482666", "managed_user_id": "", "name": "用户配置 1", "network_pbs": {"354bcb40": {"last_updated": "13396453029223032", "pb": 13}}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_138.0.3351.65": 4.0}, "password_breach_last_scanned_time": "13396470028762302", "password_breach_scan_trigger_last_reset_time": "13393656379506457", "password_breach_scan_triggered_count": 0, "password_breach_scan_triggered_password_count": 0, "password_breach_scanned": true, "password_hash_data_list": [], "signin_fre_seen_time": "13396470787369576"}, "read_aloud": {"last_used_time": "13371652542222691"}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13396470788716386"}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "pcb_supported": true}, "should_read_incoming_syncing_theme_prefs": false, "signin": {"accounts_metadata_dict": {"00034001603E8EC3": {"BookmarksExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": [], "dictionary": "", "use_spelling_service": false}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "cached_passphrase_type": 2, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "edge_account_type": 1, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "first_full_sync_completed": true, "gaia_id": "00034001603E8EC3", "has_been_enabled": true, "has_setup_completed": true, "history_edge_supported": true, "keep_everything_synced": true, "keystore_encryption_key_state": "************************************************************************************************************************************************************************************************************************************************************************************", "local_device_guids_with_timestamp": [{"cache_guid": "8lmrN7RDo1B8+Yj7zlKmuw==", "timestamp": 155051}], "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "transport_data_per_account": {"B5Hy07bzVmNv71TgCiGplKEdl0ZoALWmkektT6IjRgE=": {"sync.bag_of_chips": "", "sync.birthday": "ProductionEnvironmentDefinition_1693909318403", "sync.cache_guid": "8lmrN7RDo1B8+Yj7zlKmuw==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "***********"}}, "typed_urls": true}, "sync_consent_recorded": true, "sync_profile_info": {"edge_ci_consent_last_modified_date": "*****************", "edge_ci_consent_last_shown_date": "*****************", "edge_ci_is_option_explicitly_selectedby_user": false, "edge_san_consent_last_modified_date": "*****************", "edge_san_consent_last_shown_date": "*****************", "edge_san_is_option_explicitly_selectedby_user": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "tab_groups_migration_version": 3, "third_party_search": {"consented": false}, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled": true, "personalization_data_consent_enabled_last_known_value": true}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "link_handling_info": {"enabled_for_installed_apps": true}}}