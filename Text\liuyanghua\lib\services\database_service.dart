import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/song.dart';
import '../models/playlist.dart';

/// 数据库服务类 - 管理SQLite数据库操作
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  /// 获取数据库实例
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'music_player.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  /// 创建数据库表
  Future<void> _onCreate(Database db, int version) async {
    // 创建歌曲表
    await db.execute('''
      CREATE TABLE songs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        artist TEXT NOT NULL,
        album TEXT NOT NULL,
        file_path TEXT NOT NULL UNIQUE,
        duration INTEGER NOT NULL,
        album_art TEXT,
        date_added TEXT NOT NULL,
        is_favorite INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // 创建歌单表
    await db.execute('''
      CREATE TABLE playlists(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        date_created TEXT NOT NULL,
        date_modified TEXT NOT NULL
      )
    ''');

    // 创建歌单-歌曲关联表
    await db.execute('''
      CREATE TABLE playlist_songs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        playlist_id INTEGER NOT NULL,
        song_id INTEGER NOT NULL,
        position INTEGER NOT NULL,
        FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
        FOREIGN KEY (song_id) REFERENCES songs (id) ON DELETE CASCADE,
        UNIQUE(playlist_id, song_id)
      )
    ''');
  }

  // ========== 歌曲相关操作 ==========

  /// 插入歌曲
  Future<int> insertSong(Song song) async {
    final db = await database;
    try {
      return await db.insert('songs', song.toMap());
    } catch (e) {
      // 如果歌曲已存在，更新它
      await updateSong(song);
      final List<Map<String, dynamic>> maps = await db.query(
        'songs',
        where: 'file_path = ?',
        whereArgs: [song.filePath],
      );
      return maps.first['id'];
    }
  }

  /// 获取所有歌曲
  Future<List<Song>> getAllSongs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('songs');
    return List.generate(maps.length, (i) => Song.fromMap(maps[i]));
  }

  /// 根据ID获取歌曲
  Future<Song?> getSongById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'songs',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Song.fromMap(maps.first);
    }
    return null;
  }

  /// 更新歌曲
  Future<void> updateSong(Song song) async {
    final db = await database;
    await db.update(
      'songs',
      song.toMap(),
      where: 'id = ?',
      whereArgs: [song.id],
    );
  }

  /// 删除歌曲
  Future<void> deleteSong(int id) async {
    final db = await database;
    await db.delete(
      'songs',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// 搜索歌曲
  Future<List<Song>> searchSongs(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'songs',
      where: 'title LIKE ? OR artist LIKE ? OR album LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) => Song.fromMap(maps[i]));
  }

  /// 获取收藏的歌曲
  Future<List<Song>> getFavoriteSongs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'songs',
      where: 'is_favorite = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) => Song.fromMap(maps[i]));
  }

  /// 切换歌曲收藏状态
  Future<void> toggleSongFavorite(int songId) async {
    final song = await getSongById(songId);
    if (song != null) {
      await updateSong(song.copyWith(isFavorite: !song.isFavorite));
    }
  }

  // ========== 歌单相关操作 ==========

  /// 创建歌单
  Future<int> createPlaylist(Playlist playlist) async {
    final db = await database;
    return await db.insert('playlists', playlist.toMap());
  }

  /// 获取所有歌单
  Future<List<Playlist>> getAllPlaylists() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('playlists');
    List<Playlist> playlists = [];

    for (var map in maps) {
      final playlist = Playlist.fromMap(map);
      final songs = await getPlaylistSongs(playlist.id!);
      playlists.add(playlist.copyWith(songs: songs));
    }

    return playlists;
  }

  /// 获取歌单中的歌曲
  Future<List<Song>> getPlaylistSongs(int playlistId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT s.* FROM songs s
      INNER JOIN playlist_songs ps ON s.id = ps.song_id
      WHERE ps.playlist_id = ?
      ORDER BY ps.position
    ''', [playlistId]);

    return List.generate(maps.length, (i) => Song.fromMap(maps[i]));
  }

  /// 添加歌曲到歌单
  Future<void> addSongToPlaylist(int playlistId, int songId) async {
    final db = await database;

    // 获取当前歌单中歌曲的最大位置
    final List<Map<String, dynamic>> positionMaps = await db.query(
      'playlist_songs',
      columns: ['MAX(position) as max_position'],
      where: 'playlist_id = ?',
      whereArgs: [playlistId],
    );

    int nextPosition = (positionMaps.first['max_position'] ?? -1) + 1;

    await db.insert('playlist_songs', {
      'playlist_id': playlistId,
      'song_id': songId,
      'position': nextPosition,
    });

    // 更新歌单修改时间
    await db.update(
      'playlists',
      {'date_modified': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [playlistId],
    );
  }

  /// 从歌单移除歌曲
  Future<void> removeSongFromPlaylist(int playlistId, int songId) async {
    final db = await database;
    await db.delete(
      'playlist_songs',
      where: 'playlist_id = ? AND song_id = ?',
      whereArgs: [playlistId, songId],
    );

    // 更新歌单修改时间
    await db.update(
      'playlists',
      {'date_modified': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [playlistId],
    );
  }

  /// 删除歌单
  Future<void> deletePlaylist(int playlistId) async {
    final db = await database;
    await db.delete(
      'playlists',
      where: 'id = ?',
      whereArgs: [playlistId],
    );
  }

  /// 更新歌单信息
  Future<void> updatePlaylist(Playlist playlist) async {
    final db = await database;
    await db.update(
      'playlists',
      playlist.toMap(),
      where: 'id = ?',
      whereArgs: [playlist.id],
    );
  }

  /// 清空数据库
  Future<void> clearDatabase() async {
    final db = await database;
    await db.delete('playlist_songs');
    await db.delete('songs');
    await db.delete('playlists');
  }
}
