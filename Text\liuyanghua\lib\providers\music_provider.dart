import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import '../models/song.dart';
import '../models/playlist.dart';
import '../services/database_service.dart';
import '../services/audio_service.dart';
import '../services/file_scanner_service.dart';

/// 音乐播放状态管理类
class MusicProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final AudioPlayerService _audioService = AudioPlayerService();
  final FileScannerService _scannerService = FileScannerService();

  // 数据状态
  List<Song> _allSongs = [];
  List<Playlist> _playlists = [];
  List<Song> _favoriteSongs = [];
  List<Song> _searchResults = [];

  // 播放状态
  Song? _currentSong;
  bool _isPlaying = false;
  Duration _currentPosition = Duration.zero;
  Duration? _currentDuration;
  List<Song> _currentPlaylist = [];
  int _currentIndex = 0;
  LoopMode _loopMode = LoopMode.off;
  bool _isShuffleEnabled = false;

  // 扫描状态
  bool _isScanning = false;
  int _scanProgress = 0;
  String _currentScanFile = '';

  // Getters
  List<Song> get allSongs => _allSongs;
  List<Playlist> get playlists => _playlists;
  List<Song> get favoriteSongs => _favoriteSongs;
  List<Song> get searchResults => _searchResults;
  
  Song? get currentSong => _currentSong;
  bool get isPlaying => _isPlaying;
  Duration get currentPosition => _currentPosition;
  Duration? get currentDuration => _currentDuration;
  List<Song> get currentPlaylist => _currentPlaylist;
  int get currentIndex => _currentIndex;
  LoopMode get loopMode => _loopMode;
  bool get isShuffleEnabled => _isShuffleEnabled;
  
  bool get isScanning => _isScanning;
  int get scanProgress => _scanProgress;
  String get currentScanFile => _currentScanFile;

  /// 初始化
  Future<void> initialize() async {
    await _audioService.initialize();
    await _loadData();
    _setupAudioListeners();
  }

  /// 设置音频服务监听器
  void _setupAudioListeners() {
    _audioService.isPlayingStream.listen((playing) {
      _isPlaying = playing;
      notifyListeners();
    });

    _audioService.currentSongStream.listen((song) {
      _currentSong = song;
      notifyListeners();
    });

    _audioService.positionStream.listen((position) {
      _currentPosition = position;
      notifyListeners();
    });

    _audioService.durationStream.listen((duration) {
      _currentDuration = duration;
      notifyListeners();
    });

    _audioService.playlistStream.listen((playlist) {
      _currentPlaylist = playlist;
      notifyListeners();
    });

    _audioService.currentIndexStream.listen((index) {
      _currentIndex = index;
      notifyListeners();
    });

    _audioService.loopModeStream.listen((mode) {
      _loopMode = mode;
      notifyListeners();
    });

    _audioService.shuffleModeStream.listen((enabled) {
      _isShuffleEnabled = enabled;
      notifyListeners();
    });
  }

  /// 加载数据
  Future<void> _loadData() async {
    await loadSongs();
    await loadPlaylists();
    await loadFavoriteSongs();
  }

  // ========== 歌曲相关操作 ==========

  /// 加载所有歌曲
  Future<void> loadSongs() async {
    try {
      _allSongs = await _databaseService.getAllSongs();
      notifyListeners();
    } catch (e) {
      print('加载歌曲失败: $e');
    }
  }

  /// 扫描音频文件
  Future<void> scanAudioFiles() async {
    _isScanning = true;
    _scanProgress = 0;
    _currentScanFile = '';
    notifyListeners();

    try {
      await _scannerService.scanAudioFiles(
        onProgress: (progress) {
          _scanProgress = progress;
          notifyListeners();
        },
        onCurrentFile: (file) {
          _currentScanFile = file;
          notifyListeners();
        },
      );
      
      await loadSongs();
    } catch (e) {
      print('扫描音频文件失败: $e');
    } finally {
      _isScanning = false;
      _scanProgress = 0;
      _currentScanFile = '';
      notifyListeners();
    }
  }

  /// 搜索歌曲
  Future<void> searchSongs(String query) async {
    try {
      if (query.isEmpty) {
        _searchResults = [];
      } else {
        _searchResults = await _databaseService.searchSongs(query);
      }
      notifyListeners();
    } catch (e) {
      print('搜索歌曲失败: $e');
    }
  }

  /// 切换歌曲收藏状态
  Future<void> toggleSongFavorite(Song song) async {
    try {
      await _databaseService.toggleSongFavorite(song.id!);
      await loadSongs();
      await loadFavoriteSongs();
    } catch (e) {
      print('切换收藏状态失败: $e');
    }
  }

  /// 加载收藏歌曲
  Future<void> loadFavoriteSongs() async {
    try {
      _favoriteSongs = await _databaseService.getFavoriteSongs();
      notifyListeners();
    } catch (e) {
      print('加载收藏歌曲失败: $e');
    }
  }

  // ========== 播放控制 ==========

  /// 播放歌曲
  Future<void> playSong(Song song) async {
    await _audioService.playSong(song);
  }

  /// 播放歌曲列表
  Future<void> playPlaylist(List<Song> songs, {int startIndex = 0}) async {
    await _audioService.playPlaylist(songs, startIndex: startIndex);
  }

  /// 播放/暂停
  Future<void> togglePlayPause() async {
    await _audioService.togglePlayPause();
  }

  /// 下一首
  Future<void> skipToNext() async {
    await _audioService.skipToNext();
  }

  /// 上一首
  Future<void> skipToPrevious() async {
    await _audioService.skipToPrevious();
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    await _audioService.seekTo(position);
  }

  /// 跳转到指定歌曲
  Future<void> seekToIndex(int index) async {
    await _audioService.seekToIndex(index);
  }

  /// 切换循环模式
  Future<void> toggleLoopMode() async {
    await _audioService.toggleLoopMode();
  }

  /// 切换随机播放
  Future<void> toggleShuffleMode() async {
    await _audioService.toggleShuffleMode();
  }

  // ========== 歌单相关操作 ==========

  /// 加载歌单
  Future<void> loadPlaylists() async {
    try {
      _playlists = await _databaseService.getAllPlaylists();
      notifyListeners();
    } catch (e) {
      print('加载歌单失败: $e');
    }
  }

  /// 创建歌单
  Future<void> createPlaylist(String name, {String? description}) async {
    try {
      final playlist = Playlist(name: name, description: description);
      await _databaseService.createPlaylist(playlist);
      await loadPlaylists();
    } catch (e) {
      print('创建歌单失败: $e');
    }
  }

  /// 删除歌单
  Future<void> deletePlaylist(int playlistId) async {
    try {
      await _databaseService.deletePlaylist(playlistId);
      await loadPlaylists();
    } catch (e) {
      print('删除歌单失败: $e');
    }
  }

  /// 添加歌曲到歌单
  Future<void> addSongToPlaylist(int playlistId, Song song) async {
    try {
      await _databaseService.addSongToPlaylist(playlistId, song.id!);
      await loadPlaylists();
    } catch (e) {
      print('添加歌曲到歌单失败: $e');
    }
  }

  /// 从歌单移除歌曲
  Future<void> removeSongFromPlaylist(int playlistId, Song song) async {
    try {
      await _databaseService.removeSongFromPlaylist(playlistId, song.id!);
      await loadPlaylists();
    } catch (e) {
      print('从歌单移除歌曲失败: $e');
    }
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
