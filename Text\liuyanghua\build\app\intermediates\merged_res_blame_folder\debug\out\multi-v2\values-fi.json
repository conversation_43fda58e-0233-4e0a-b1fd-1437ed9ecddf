{"logs": [{"outputFile": "com.example.liuyanghua.app-mergeDebugResources-26:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\889ae3adf7a24645889ee22f4dad2cac\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,18", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,1446", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,1542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d61e896a1291a8958fc90251d79de4b7\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "789,854,913,975,1042,1119,1189,1283,1375", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "849,908,970,1037,1114,1184,1278,1370,1441"}}]}]}