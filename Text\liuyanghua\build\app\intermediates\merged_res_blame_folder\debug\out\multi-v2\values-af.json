{"logs": [{"outputFile": "com.example.liuyanghua.app-mergeDebugResources-26:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d61e896a1291a8958fc90251d79de4b7\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "787,856,916,982,1049,1124,1194,1283,1367", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "851,911,977,1044,1119,1189,1278,1362,1434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\889ae3adf7a24645889ee22f4dad2cac\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,18", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,1439", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,1535"}}]}]}