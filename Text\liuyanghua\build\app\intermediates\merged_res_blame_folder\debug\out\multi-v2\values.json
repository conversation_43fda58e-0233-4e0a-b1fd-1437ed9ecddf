{"logs": [{"outputFile": "com.example.liuyanghua.app-mergeDebugResources-26:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\hua\\Cangjie-Examples\\Text\\liuyanghua\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "169,173", "startColumns": "4,4", "startOffsets": "8895,9076", "endLines": "172,175", "endColumns": "12,12", "endOffsets": "9071,9240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a1fd92b89d0c194f8fe8fdca4d20e2ee\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "151", "startColumns": "4", "startOffsets": "7584", "endColumns": "82", "endOffsets": "7662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eb94ed51b8e22ca5bfbece3643a9b72a\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "61,62,63,125,149,181,183,184,189,191", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2146,2235,2306,6155,7454,9563,9739,9861,10123,10318", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "2230,2301,2374,6212,7509,9624,9856,9917,10184,10380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8859fd2468ade3ef2655d1f63d6737f2\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,121,194,200,323,331,346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,785,1099,1287,1474,1527,1587,1639,1684,5968,10524,10719,14961,15243,15857", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,121,199,204,330,345,361", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1094,1282,1469,1522,1582,1634,1679,1718,6023,10714,10872,15238,15852,16506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\889ae3adf7a24645889ee22f4dad2cac\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,57,58,59,60,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,150,152,153,154,155,156,157,158,168,176,177,182,185,190,192,193,205,211,221,254,284,317", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "725,1723,1795,1883,1948,2014,2083,2379,2449,2517,2589,2659,2720,2794,2867,2928,2989,3051,3115,3177,3238,3306,3406,3466,3532,3605,3674,3731,3783,3845,3917,3993,4058,4117,4176,4236,4296,4356,4416,4476,4536,4596,4656,4716,4776,4835,4895,4955,5015,5075,5135,5195,5255,5315,5375,5435,5494,5554,5614,5673,5732,5791,5850,5909,6085,6120,6324,6379,6442,6497,6555,6613,6674,6737,6794,6845,6895,6956,7013,7079,7113,7148,7514,7667,7734,7806,7875,7944,8018,8090,8824,9245,9362,9629,9922,10189,10385,10457,10877,11080,11381,13112,14112,14794", "endLines": "25,55,56,57,58,59,60,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,150,152,153,154,155,156,157,158,168,176,180,182,188,190,192,193,210,220,253,274,316,322", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "780,1790,1878,1943,2009,2078,2141,2444,2512,2584,2654,2715,2789,2862,2923,2984,3046,3110,3172,3233,3301,3401,3461,3527,3600,3669,3726,3778,3840,3912,3988,4053,4112,4171,4231,4291,4351,4411,4471,4531,4591,4651,4711,4771,4830,4890,4950,5010,5070,5130,5190,5250,5310,5370,5430,5489,5549,5609,5668,5727,5786,5845,5904,5963,6115,6150,6374,6437,6492,6550,6608,6669,6732,6789,6840,6890,6951,7008,7074,7108,7143,7178,7579,7729,7801,7870,7939,8013,8085,8173,8890,9357,9558,9734,10118,10313,10452,10519,11075,11376,13107,13788,14789,14956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ca4d40f38b71d4c621d36eba6fe2f4a4\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "122,127,148,275,280", "startColumns": "4,4,4,4,4", "startOffsets": "6028,6259,7390,13793,13963", "endLines": "122,127,148,279,283", "endColumns": "56,64,63,24,24", "endOffsets": "6080,6319,7449,13958,14107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\12d0e8d71b0600cec5ba2edcf29e905c\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "144", "startColumns": "4", "startOffsets": "7183", "endColumns": "42", "endOffsets": "7221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40a45e686cd0e99ac2a33b1453f47939\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "146", "startColumns": "4", "startOffsets": "7286", "endColumns": "53", "endOffsets": "7335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90da4fa7ab29bed17775e89b3905b1f1\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "126,145", "startColumns": "4,4", "startOffsets": "6217,7226", "endColumns": "41,59", "endOffsets": "6254,7281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d61e896a1291a8958fc90251d79de4b7\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8178,8248,8310,8375,8439,8516,8581,8671,8755", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "8243,8305,8370,8434,8511,8576,8666,8750,8819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b5a837770709484bd30395a2788a4e5\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "147", "startColumns": "4", "startOffsets": "7340", "endColumns": "49", "endOffsets": "7385"}}]}]}