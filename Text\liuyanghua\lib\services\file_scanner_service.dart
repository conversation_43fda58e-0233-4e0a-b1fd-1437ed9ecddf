import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/song.dart';
import 'database_service.dart';

/// 文件扫描服务类 - 扫描设备中的音频文件
class FileScannerService {
  static final FileScannerService _instance = FileScannerService._internal();
  factory FileScannerService() => _instance;
  FileScannerService._internal();

  final DatabaseService _databaseService = DatabaseService();
  
  // 支持的音频格式
  static const List<String> _supportedExtensions = [
    '.mp3',
    '.m4a',
    '.aac',
    '.wav',
    '.flac',
    '.ogg',
  ];

  /// 请求存储权限
  Future<bool> requestStoragePermission() async {
    // 检查当前权限状态
    var status = await Permission.storage.status;
    
    if (status.isGranted) {
      return true;
    }
    
    // 请求权限
    if (status.isDenied) {
      status = await Permission.storage.request();
    }
    
    // 对于Android 13+，需要请求音频权限
    if (Platform.isAndroid) {
      var audioStatus = await Permission.audio.status;
      if (audioStatus.isDenied) {
        audioStatus = await Permission.audio.request();
      }
      return status.isGranted || audioStatus.isGranted;
    }
    
    return status.isGranted;
  }

  /// 扫描设备中的音频文件
  Future<List<Song>> scanAudioFiles({
    Function(int)? onProgress,
    Function(String)? onCurrentFile,
  }) async {
    // 检查权限
    bool hasPermission = await requestStoragePermission();
    if (!hasPermission) {
      throw Exception('需要存储权限才能扫描音频文件');
    }

    List<Song> songs = [];
    List<String> scannedPaths = [];

    try {
      // 获取常见的音频文件目录
      List<Directory> searchDirectories = await _getAudioDirectories();
      
      int totalFiles = 0;
      int processedFiles = 0;

      // 首先计算总文件数
      for (Directory dir in searchDirectories) {
        if (await dir.exists()) {
          totalFiles += await _countAudioFiles(dir);
        }
      }

      // 扫描每个目录
      for (Directory dir in searchDirectories) {
        if (await dir.exists()) {
          List<Song> dirSongs = await _scanDirectory(
            dir,
            scannedPaths,
            (current) {
              processedFiles++;
              onProgress?.call((processedFiles * 100 / totalFiles).round());
              onCurrentFile?.call(current);
            },
          );
          songs.addAll(dirSongs);
        }
      }

      // 保存到数据库
      for (Song song in songs) {
        try {
          await _databaseService.insertSong(song);
        } catch (e) {
          print('保存歌曲失败: ${song.title} - $e');
        }
      }

      return songs;
    } catch (e) {
      print('扫描音频文件时出错: $e');
      rethrow;
    }
  }

  /// 获取音频文件搜索目录
  Future<List<Directory>> _getAudioDirectories() async {
    List<Directory> directories = [];

    try {
      // 外部存储目录
      Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        directories.add(externalDir);
      }

      // 常见的音乐目录
      List<String> commonMusicPaths = [
        '/storage/emulated/0/Music',
        '/storage/emulated/0/Download',
        '/storage/emulated/0/Downloads',
        '/sdcard/Music',
        '/sdcard/Download',
        '/sdcard/Downloads',
      ];

      for (String path in commonMusicPaths) {
        Directory dir = Directory(path);
        if (await dir.exists()) {
          directories.add(dir);
        }
      }

      // 应用文档目录
      Directory appDocDir = await getApplicationDocumentsDirectory();
      directories.add(appDocDir);

    } catch (e) {
      print('获取音频目录时出错: $e');
    }

    return directories;
  }

  /// 计算目录中的音频文件数量
  Future<int> _countAudioFiles(Directory directory) async {
    int count = 0;
    try {
      await for (FileSystemEntity entity in directory.list(recursive: true)) {
        if (entity is File && _isAudioFile(entity.path)) {
          count++;
        }
      }
    } catch (e) {
      print('计算文件数量时出错: $e');
    }
    return count;
  }

  /// 扫描指定目录
  Future<List<Song>> _scanDirectory(
    Directory directory,
    List<String> scannedPaths,
    Function(String) onProgress,
  ) async {
    List<Song> songs = [];

    try {
      await for (FileSystemEntity entity in directory.list(recursive: true)) {
        if (entity is File && _isAudioFile(entity.path)) {
          // 避免重复扫描
          if (scannedPaths.contains(entity.path)) {
            continue;
          }
          scannedPaths.add(entity.path);

          onProgress(entity.path);

          try {
            Song? song = await _createSongFromFile(entity);
            if (song != null) {
              songs.add(song);
            }
          } catch (e) {
            print('处理文件失败: ${entity.path} - $e');
          }
        }
      }
    } catch (e) {
      print('扫描目录失败: ${directory.path} - $e');
    }

    return songs;
  }

  /// 检查是否为音频文件
  bool _isAudioFile(String filePath) {
    String extension = filePath.toLowerCase();
    return _supportedExtensions.any((ext) => extension.endsWith(ext));
  }

  /// 从文件创建Song对象
  Future<Song?> _createSongFromFile(File file) async {
    try {
      // 获取文件信息
      FileStat stat = await file.stat();
      String fileName = file.path.split('/').last;
      
      // 解析文件名获取基本信息
      Map<String, String> metadata = _parseFileName(fileName);
      
      // 尝试获取音频时长（简化版本，实际项目中可以使用audiotags包）
      int duration = await _getAudioDuration(file);

      return Song(
        title: metadata['title'] ?? _removeExtension(fileName),
        artist: metadata['artist'] ?? 'Unknown Artist',
        album: metadata['album'] ?? 'Unknown Album',
        filePath: file.path,
        duration: duration,
        dateAdded: stat.modified,
      );
    } catch (e) {
      print('创建Song对象失败: ${file.path} - $e');
      return null;
    }
  }

  /// 解析文件名获取元数据
  Map<String, String> _parseFileName(String fileName) {
    Map<String, String> metadata = {};
    
    // 移除扩展名
    String nameWithoutExt = _removeExtension(fileName);
    
    // 尝试解析 "Artist - Title" 格式
    if (nameWithoutExt.contains(' - ')) {
      List<String> parts = nameWithoutExt.split(' - ');
      if (parts.length >= 2) {
        metadata['artist'] = parts[0].trim();
        metadata['title'] = parts[1].trim();
      }
    } else {
      metadata['title'] = nameWithoutExt;
    }
    
    return metadata;
  }

  /// 移除文件扩展名
  String _removeExtension(String fileName) {
    int lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0) {
      return fileName.substring(0, lastDotIndex);
    }
    return fileName;
  }

  /// 获取音频时长（简化版本）
  Future<int> _getAudioDuration(File file) async {
    try {
      // 这里是简化版本，返回一个估算值
      // 实际项目中应该使用专门的音频库来获取准确时长
      FileStat stat = await file.stat();
      int fileSizeKB = stat.size ~/ 1024;
      
      // 粗略估算：假设平均比特率为128kbps
      // 时长(秒) ≈ 文件大小(KB) / (比特率(kbps) / 8)
      int estimatedDuration = (fileSizeKB / (128 / 8)).round();
      
      // 限制在合理范围内
      return estimatedDuration.clamp(1, 3600); // 1秒到1小时
    } catch (e) {
      print('获取音频时长失败: $e');
      return 180; // 默认3分钟
    }
  }

  /// 刷新音乐库
  Future<List<Song>> refreshMusicLibrary({
    Function(int)? onProgress,
    Function(String)? onCurrentFile,
  }) async {
    // 清空现有数据
    await _databaseService.clearDatabase();
    
    // 重新扫描
    return await scanAudioFiles(
      onProgress: onProgress,
      onCurrentFile: onCurrentFile,
    );
  }
}
