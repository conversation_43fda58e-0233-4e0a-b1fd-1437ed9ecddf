import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:just_audio/just_audio.dart';
import '../providers/music_provider.dart';

/// 播放器屏幕 - 全屏播放界面
class PlayerScreen extends StatelessWidget {
  const PlayerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('正在播放'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<MusicProvider>(
        builder: (context, musicProvider, child) {
          final currentSong = musicProvider.currentSong;
          if (currentSong == null) {
            return const Center(
              child: Text('没有正在播放的歌曲'),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const Spacer(),
                // 专辑封面
                _buildAlbumArt(context, currentSong.albumArt),
                const SizedBox(height: 32),
                // 歌曲信息
                _buildSongInfo(context, currentSong),
                const SizedBox(height: 32),
                // 进度条
                _buildProgressSlider(context, musicProvider),
                const SizedBox(height: 32),
                // 播放控制按钮
                _buildPlayControls(context, musicProvider),
                const SizedBox(height: 24),
                // 模式控制按钮
                _buildModeControls(context, musicProvider),
                const Spacer(),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建专辑封面
  Widget _buildAlbumArt(BuildContext context, String? albumArt) {
    final size = MediaQuery.of(context).size.width * 0.7;
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: albumArt != null && albumArt.isNotEmpty
            ? Image.asset(
                albumArt,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAlbumArt(context);
                },
              )
            : _buildDefaultAlbumArt(context),
      ),
    );
  }

  /// 构建默认专辑封面
  Widget _buildDefaultAlbumArt(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary.withOpacity(0.8),
            colorScheme.secondary.withOpacity(0.8),
          ],
        ),
      ),
      child: Icon(
        Icons.music_note,
        size: 80,
        color: colorScheme.onPrimary,
      ),
    );
  }

  /// 构建歌曲信息
  Widget _buildSongInfo(BuildContext context, currentSong) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Text(
          currentSong.title,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          currentSong.artist,
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          currentSong.album,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.5),
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 构建进度滑块
  Widget _buildProgressSlider(BuildContext context, MusicProvider musicProvider) {
    final theme = Theme.of(context);
    final duration = musicProvider.currentDuration;
    final position = musicProvider.currentPosition;

    return Column(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            trackHeight: 4,
          ),
          child: Slider(
            value: duration != null && duration.inMilliseconds > 0
                ? (position.inMilliseconds / duration.inMilliseconds).clamp(0.0, 1.0)
                : 0.0,
            onChanged: (value) {
              if (duration != null) {
                final newPosition = Duration(
                  milliseconds: (value * duration.inMilliseconds).round(),
                );
                musicProvider.seekTo(newPosition);
              }
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(position),
                style: theme.textTheme.bodySmall,
              ),
              Text(
                _formatDuration(duration ?? Duration.zero),
                style: theme.textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建播放控制按钮
  Widget _buildPlayControls(BuildContext context, MusicProvider musicProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          icon: const Icon(Icons.skip_previous),
          iconSize: 48,
          onPressed: musicProvider.skipToPrevious,
        ),
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).colorScheme.primary,
          ),
          child: IconButton(
            icon: Icon(
              musicProvider.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            iconSize: 56,
            onPressed: musicProvider.togglePlayPause,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.skip_next),
          iconSize: 48,
          onPressed: musicProvider.skipToNext,
        ),
      ],
    );
  }

  /// 构建模式控制按钮
  Widget _buildModeControls(BuildContext context, MusicProvider musicProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          icon: Icon(
            musicProvider.isShuffleEnabled ? Icons.shuffle : Icons.shuffle,
            color: musicProvider.isShuffleEnabled
                ? Theme.of(context).colorScheme.primary
                : null,
          ),
          onPressed: musicProvider.toggleShuffleMode,
        ),
        IconButton(
          icon: Icon(_getLoopIcon(musicProvider.loopMode)),
          color: musicProvider.loopMode != LoopMode.off
              ? Theme.of(context).colorScheme.primary
              : null,
          onPressed: musicProvider.toggleLoopMode,
        ),
        IconButton(
          icon: const Icon(Icons.queue_music),
          onPressed: () {
            // TODO: 显示播放队列
          },
        ),
      ],
    );
  }

  /// 获取循环模式图标
  IconData _getLoopIcon(LoopMode loopMode) {
    switch (loopMode) {
      case LoopMode.off:
        return Icons.repeat;
      case LoopMode.all:
        return Icons.repeat;
      case LoopMode.one:
        return Icons.repeat_one;
    }
  }

  /// 格式化时长
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
