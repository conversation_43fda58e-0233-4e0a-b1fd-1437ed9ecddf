package Text

import std.console.*
import std.convert.*

main() {
    println("====================================")
    println("        英语单词拼写挑战            ")
    println("====================================")
    print("请输入你的名字：")
    let name = Console.stdIn.readln().getOrThrow()
    println("你好，" + name + "！欢迎参加英语单词拼写挑战游戏！")

    // 游戏变量初始化
    var playerScore = 0
    var totalQuestions = 0
    var round = 1
    var history = Array<String>(20) { _ => "" }  // 存储历史记录
    var historyCount = 0
    var playAgain = true

    println("游戏规则：")
    println("1. 系统会显示单词的中文意思，你需要正确拼写出英文单词")
    println("2. 每答对一题得10分")
    println("3. 输入答案或输入'q'退出游戏")
    println("4. 达到50分即可挑战成功！")

    while (playAgain) {
        println("\n====================================")
        println("           第 " + round.toString() + " 轮挑战           ")
        println("====================================")
        playerScore = 0
        totalQuestions = 0
        round = 1  // 这里应该是从1开始，但循环内会递增，可能需要调整
        historyCount = 0

        // 使用一个固定循环次数来替代之前的while(true)，避免复杂的控制流
        for (i in 1..10) {  // 最多10题
            println("\n第 " + i.toString() + " 题：")
            println("当前得分：" + playerScore.toString() + " 分")

            // 预设单词库 - 使用简单的数组和索引方式
            let chineseWords = ["苹果", "香蕉", "猫", "狗", "大象", "鱼", "长颈鹿", "房子", "冰淇淋", "果酱"]
            let englishWords = ["apple", "banana", "cat", "dog", "elephant", "fish", "giraffe", "house", "ice cream", "jam"]

            // 简单选择单词 - 不使用复杂的随机算法以避免类型问题
            let wordIndex = (i - 1) % chineseWords.size  // 简单轮换选择单词
            let chineseWord = chineseWords[wordIndex]
            let englishWord = englishWords[wordIndex]

            // 显示中文提示
            println("请拼写单词：【" + chineseWord + "】")

            // 获取用户输入
            print("你的答案：")
            let input = Console.stdIn.readln().getOrThrow()

            // 检查是否退出
            if (input == "q") {
                println("你选择退出挑战。")
                break
            }

            totalQuestions++
            let playerAnswer = input
            let correctAnswer = englishWord

            // 判断答案是否正确
            var result = ""
            if (playerAnswer == correctAnswer) {
                result = "回答正确！+10分"
                playerScore += 10
            } else {
                result = "回答错误！正确答案是：" + englishWord
            }

            println("结果：" + result)
            println("当前得分：" + playerScore.toString() + " 分")

            // 记录历史
            let record = "第" + i.toString() + "题：【" + chineseWord + "】" +
                    " 你的答案：" + playerAnswer +
                    " 正确答案：" + englishWord +
                    " " + result
            history[historyCount] = record
            historyCount++

            // 每3题显示一次历史记录
            if (i % 3 == 0 && historyCount > 0) {
                println("\n==== 答题历史 ====")
                for (j in 0..historyCount-1) {
                    println(history[j])
                }
            }

            // 检查是否达到目标分数
            if (playerScore >= 50) {
                println("\n恭喜！你已达到50分，挑战成功！")
                break
            }
            
            round++  // 增加回合数
        }

        // 游戏结束统计
        println("\n====================================")
        println("           挑战结束               ")
        println("====================================")
        println("最终得分：" + playerScore.toString() + " 分")
        println("总题数：" + totalQuestions.toString() + " 题")

        // 计算正确率
        var accuracy = 0
        if (totalQuestions > 0) {
            accuracy = (playerScore / 10 * 100) / totalQuestions
        }
        println("正确率：" + accuracy.toString() + "%")

        // 根据得分给出评价
        if (playerScore >= 50) {
            println("太棒了！你成功完成了挑战！")
        } else if (totalQuestions > 0) {
            println("继续努力，下次一定能成功！")
        } else {
            println("你选择了退出挑战。")
        }

        // 显示完整历史记录
        println("\n==== 你的答题历史 ====")
        for (i in 0..historyCount-1) {
            println(history[i])
        }

        // 询问是否再玩一次
        print("\n是否开始新的挑战？(y/n)：")
        let again = Console.stdIn.readln().getOrThrow()
        if (again != "y" && again != "Y") {
            playAgain = false
        }
    }

    println("\n====================================")
    println("       感谢参与单词拼写挑战       ")
    println("       欢迎下次再来！             ")
    println("====================================")
}