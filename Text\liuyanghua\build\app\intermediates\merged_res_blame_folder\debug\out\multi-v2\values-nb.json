{"logs": [{"outputFile": "com.example.liuyanghua.app-mergeDebugResources-26:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\889ae3adf7a24645889ee22f4dad2cac\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,18", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,1474", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,1570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d61e896a1291a8958fc90251d79de4b7\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "781,857,919,983,1054,1134,1212,1306,1403", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "852,914,978,1049,1129,1207,1301,1398,1469"}}]}]}