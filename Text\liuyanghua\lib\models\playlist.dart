import 'song.dart';

/// 歌单数据模型
class Playlist {
  final int? id;
  final String name;
  final String? description;
  final DateTime dateCreated;
  final DateTime dateModified;
  final List<Song> songs;

  Playlist({
    this.id,
    required this.name,
    this.description,
    DateTime? dateCreated,
    DateTime? dateModified,
    List<Song>? songs,
  })  : dateCreated = dateCreated ?? DateTime.now(),
        dateModified = dateModified ?? DateTime.now(),
        songs = songs ?? [];

  /// 从数据库Map创建Playlist对象
  factory Playlist.fromMap(Map<String, dynamic> map) {
    return Playlist(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      dateCreated: DateTime.parse(map['date_created']),
      dateModified: DateTime.parse(map['date_modified']),
      songs: [], // 歌曲列表需要单独查询
    );
  }

  /// 转换为数据库Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'date_created': dateCreated.toIso8601String(),
      'date_modified': dateModified.toIso8601String(),
    };
  }

  /// 创建副本并修改某些属性
  Playlist copyWith({
    int? id,
    String? name,
    String? description,
    DateTime? dateCreated,
    DateTime? dateModified,
    List<Song>? songs,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      dateCreated: dateCreated ?? this.dateCreated,
      dateModified: dateModified ?? this.dateModified,
      songs: songs ?? List.from(this.songs),
    );
  }

  /// 添加歌曲到歌单
  Playlist addSong(Song song) {
    final newSongs = List<Song>.from(songs);
    if (!newSongs.contains(song)) {
      newSongs.add(song);
    }
    return copyWith(
      songs: newSongs,
      dateModified: DateTime.now(),
    );
  }

  /// 从歌单移除歌曲
  Playlist removeSong(Song song) {
    final newSongs = List<Song>.from(songs);
    newSongs.remove(song);
    return copyWith(
      songs: newSongs,
      dateModified: DateTime.now(),
    );
  }

  /// 获取歌单总时长（秒）
  int get totalDuration {
    return songs.fold(0, (total, song) => total + song.duration);
  }

  /// 格式化总时长
  String get formattedTotalDuration {
    final totalMinutes = totalDuration ~/ 60;
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// 获取歌曲数量
  int get songCount => songs.length;

  @override
  String toString() {
    return 'Playlist{id: $id, name: $name, songCount: $songCount}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Playlist &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
