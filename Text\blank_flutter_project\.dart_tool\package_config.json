{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.19.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///D:/QQ/flutter_windows_3.27.4-stable/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///D:/QQ/flutter_windows_3.27.4-stable/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///D:/QQ/flutter_windows_3.27.4-stable/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.3.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-14.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "blank_flutter_project", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.6"}], "generated": "2025-07-08T18:18:08.177846Z", "generator": "pub", "generatorVersion": "3.6.2", "flutterRoot": "file:///D:/QQ/flutter_windows_3.27.4-stable/flutter", "flutterVersion": "3.27.4", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}