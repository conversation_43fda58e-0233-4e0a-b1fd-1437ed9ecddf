# Flutter音乐播放器

一个功能完整的Flutter音乐播放应用，支持本地音乐播放、歌单管理和收藏功能。

## 功能特性

### 核心功能
- ✅ **本地音乐播放** - 扫描并播放设备中的音频文件
- ✅ **播放控制** - 播放/暂停、上一首/下一首、进度控制
- ✅ **后台播放** - 支持后台播放和系统通知栏控制
- ✅ **歌单管理** - 创建、编辑、删除自定义歌单
- ✅ **收藏功能** - 收藏喜欢的歌曲
- ✅ **搜索功能** - 按歌名、歌手、专辑搜索
- ✅ **播放模式** - 支持顺序播放、随机播放、单曲循环

### 扩展功能
- ✅ **主题切换** - 支持浅色/深色主题
- ✅ **数据持久化** - 使用SQLite本地数据库
- ✅ **权限管理** - 智能请求存储权限
- ✅ **响应式设计** - 适配不同屏幕尺寸

## 技术架构

### 技术选型
- **框架**: Flutter 3.6.2+
- **状态管理**: Provider
- **数据库**: SQLite (sqflite)
- **音频播放**: just_audio + audio_service
- **权限管理**: permission_handler

### 项目结构
```
lib/
├── models/               # 数据模型
│   ├── song.dart        # 歌曲模型
│   └── playlist.dart    # 歌单模型
├── services/            # 业务逻辑层
│   ├── database_service.dart      # 数据库服务
│   ├── audio_service.dart         # 音频播放服务
│   └── file_scanner_service.dart  # 文件扫描服务
├── providers/           # 状态管理
│   └── music_provider.dart       # 音乐状态管理
├── screens/             # 用户界面
│   ├── home_screen.dart          # 主屏幕
│   ├── music_library_screen.dart # 音乐库
│   ├── playlists_screen.dart     # 歌单页面
│   ├── favorites_screen.dart     # 收藏页面
│   └── player_screen.dart        # 播放器页面
├── widgets/             # 可复用组件
│   ├── song_list_tile.dart       # 歌曲列表项
│   └── mini_player.dart          # 迷你播放器
└── main.dart            # 程序入口
```

### 数据库设计
- **songs表** - 存储歌曲信息（标题、歌手、专辑、文件路径等）
- **playlists表** - 存储歌单信息（名称、描述、创建时间等）
- **playlist_songs表** - 歌单与歌曲的关联表

## 运行环境

### 系统要求
- **Android**: API Level 21+ (Android 5.0+)
- **Flutter**: 3.6.2 或更高版本
- **Dart**: 3.0.0 或更高版本

### 依赖包版本
```yaml
dependencies:
  flutter: sdk: flutter
  just_audio: ^0.9.36          # 音频播放
  audio_service: ^0.18.12      # 后台音频服务
  sqflite: ^2.3.0              # SQLite数据库
  provider: ^6.1.1             # 状态管理
  permission_handler: ^11.0.1   # 权限管理
  path_provider: ^2.1.1        # 路径获取
  audiotags: ^0.0.4            # 音频元数据
```

## 安装与运行

### 1. 环境准备
```bash
# 检查Flutter环境
flutter doctor

# 确保Android开发环境正常
flutter doctor --android-licenses
```

### 2. 获取依赖
```bash
flutter pub get
```

### 3. 运行应用
```bash
# 在Android设备/模拟器上运行
flutter run

# 构建APK
flutter build apk --release
```

## 使用说明

### 首次使用
1. 启动应用后，点击"扫描音乐"按钮
2. 授予存储权限
3. 等待扫描完成，音乐文件将自动添加到音乐库

### 基本操作
- **播放音乐**: 点击歌曲列表中的任意歌曲
- **收藏歌曲**: 点击歌曲旁的爱心图标
- **创建歌单**: 在歌单页面点击"+"按钮
- **搜索音乐**: 在音乐库页面点击搜索图标

### 播放控制
- **迷你播放器**: 在底部显示当前播放信息和基本控制
- **全屏播放器**: 点击迷你播放器进入全屏播放界面
- **播放模式**: 支持顺序、随机、单曲循环三种模式

## 开发说明

### 合规性声明
- 本项目仅用于学习和演示目的
- 音乐文件来源于用户设备本地存储
- 不包含任何版权音乐内容
- 遵循Android存储权限最佳实践

### 扩展开发
项目采用分层架构设计，便于功能扩展：
- 添加在线音乐API支持
- 集成歌词显示功能
- 添加均衡器功能
- 支持更多音频格式

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 作者

刘扬华 - Flutter音乐播放器开发

---

**注意**: 本应用需要存储权限来扫描设备中的音频文件。请确保在使用前授予相应权限。
