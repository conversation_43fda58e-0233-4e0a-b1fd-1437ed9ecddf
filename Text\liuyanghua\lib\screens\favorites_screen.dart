import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/music_provider.dart';
import '../models/song.dart';
import '../widgets/song_list_tile.dart';

/// 收藏屏幕 - 显示收藏的歌曲
class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的收藏'),
        actions: [
          Consumer<MusicProvider>(
            builder: (context, musicProvider, child) {
              final favoriteSongs = musicProvider.favoriteSongs;
              if (favoriteSongs.isNotEmpty) {
                return PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'play_all':
                        musicProvider.playPlaylist(favoriteSongs);
                        break;
                      case 'shuffle_play':
                        musicProvider.playPlaylist(favoriteSongs);
                        musicProvider.toggleShuffleMode();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'play_all',
                      child: Row(
                        children: [
                          Icon(Icons.play_arrow),
                          SizedBox(width: 8),
                          Text('播放全部'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'shuffle_play',
                      child: Row(
                        children: [
                          Icon(Icons.shuffle),
                          SizedBox(width: 8),
                          Text('随机播放'),
                        ],
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Consumer<MusicProvider>(
        builder: (context, musicProvider, child) {
          final favoriteSongs = musicProvider.favoriteSongs;

          if (favoriteSongs.isEmpty) {
            return _buildEmptyView(context);
          }

          return Column(
            children: [
              // 收藏统计信息
              _buildStatsHeader(context, favoriteSongs),
              const Divider(),
              // 收藏歌曲列表
              Expanded(
                child: _buildFavoritesList(context, favoriteSongs, musicProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无收藏歌曲',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '点击歌曲旁边的爱心图标来收藏您喜欢的音乐',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计信息头部
  Widget _buildStatsHeader(BuildContext context, List<Song> favoriteSongs) {
    final theme = Theme.of(context);
    final totalDuration = favoriteSongs.fold<int>(
      0,
      (sum, song) => sum + song.duration,
    );

    final formattedDuration = _formatTotalDuration(totalDuration);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 收藏图标
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.favorite,
              color: Colors.red,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          // 统计信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '我的收藏',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${favoriteSongs.length} 首歌曲 • $formattedDuration',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          // 播放按钮
          Consumer<MusicProvider>(
            builder: (context, musicProvider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.shuffle),
                    onPressed: () {
                      musicProvider.playPlaylist(favoriteSongs);
                      musicProvider.toggleShuffleMode();
                    },
                    tooltip: '随机播放',
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      musicProvider.playPlaylist(favoriteSongs);
                    },
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('播放'),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建收藏歌曲列表
  Widget _buildFavoritesList(
    BuildContext context,
    List<Song> favoriteSongs,
    MusicProvider musicProvider,
  ) {
    return ListView.builder(
      itemCount: favoriteSongs.length,
      itemBuilder: (context, index) {
        final song = favoriteSongs[index];
        final isCurrentSong = musicProvider.currentSong?.id == song.id;

        return SongListTile(
          song: song,
          isPlaying: isCurrentSong && musicProvider.isPlaying,
          onTap: () {
            musicProvider.playPlaylist(favoriteSongs, startIndex: index);
          },
          onFavoriteToggle: () {
            musicProvider.toggleSongFavorite(song);
          },
          trailing: PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'add_to_playlist':
                  _showAddToPlaylistDialog(context, song);
                  break;
                case 'remove_favorite':
                  musicProvider.toggleSongFavorite(song);
                  break;
                case 'song_info':
                  _showSongInfoDialog(context, song);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_to_playlist',
                child: Row(
                  children: [
                    Icon(Icons.playlist_add),
                    SizedBox(width: 8),
                    Text('添加到歌单'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'remove_favorite',
                child: Row(
                  children: [
                    Icon(Icons.favorite_border),
                    SizedBox(width: 8),
                    Text('取消收藏'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'song_info',
                child: Row(
                  children: [
                    Icon(Icons.info),
                    SizedBox(width: 8),
                    Text('歌曲信息'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 格式化总时长
  String _formatTotalDuration(int totalSeconds) {
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;

    if (hours > 0) {
      return '${hours}小时${minutes}分钟';
    } else {
      return '${minutes}分钟';
    }
  }

  /// 显示添加到歌单对话框
  void _showAddToPlaylistDialog(BuildContext context, Song song) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加到歌单'),
        content: const Text('选择要添加到的歌单'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示歌曲信息对话框
  void _showSongInfoDialog(BuildContext context, Song song) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('歌曲信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('标题', song.title),
            _buildInfoRow('歌手', song.artist),
            _buildInfoRow('专辑', song.album),
            _buildInfoRow('时长', song.formattedDuration),
            _buildInfoRow('收藏时间', _formatDate(song.dateAdded)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
