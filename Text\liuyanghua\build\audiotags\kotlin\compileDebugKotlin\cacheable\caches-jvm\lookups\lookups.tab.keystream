  Build 
android.os  VERSION android.os.Build  RELEASE android.os.Build.VERSION  NonNull androidx.annotation  AudiotagsPlugin com.erikas.audiotags  
MethodChannel com.erikas.audiotags  android com.erikas.audiotags  
FlutterPlugin $com.erikas.audiotags.AudiotagsPlugin  
MethodCall $com.erikas.audiotags.AudiotagsPlugin  
MethodChannel $com.erikas.audiotags.AudiotagsPlugin  NonNull $com.erikas.audiotags.AudiotagsPlugin  Result $com.erikas.audiotags.AudiotagsPlugin  android $com.erikas.audiotags.AudiotagsPlugin  channel $com.erikas.audiotags.AudiotagsPlugin  
getANDROID $com.erikas.audiotags.AudiotagsPlugin  
getAndroid $com.erikas.audiotags.AudiotagsPlugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  
MethodChannel 	java.lang  android 	java.lang  Boolean kotlin  
MethodChannel kotlin  Nothing kotlin  String kotlin  android kotlin  
MethodChannel kotlin.annotation  android kotlin.annotation  
MethodChannel kotlin.collections  android kotlin.collections  
MethodChannel kotlin.comparisons  android kotlin.comparisons  
MethodChannel 	kotlin.io  android 	kotlin.io  
MethodChannel 
kotlin.jvm  android 
kotlin.jvm  
MethodChannel 
kotlin.ranges  android 
kotlin.ranges  
MethodChannel kotlin.sequences  android kotlin.sequences  
MethodChannel kotlin.text  android kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            