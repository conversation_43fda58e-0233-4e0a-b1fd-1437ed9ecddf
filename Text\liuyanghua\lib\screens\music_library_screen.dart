import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/music_provider.dart';
import '../models/song.dart';
import '../widgets/song_list_tile.dart';

/// 音乐库屏幕 - 显示所有歌曲
class MusicLibraryScreen extends StatefulWidget {
  const MusicLibraryScreen({super.key});

  @override
  State<MusicLibraryScreen> createState() => _MusicLibraryScreenState();
}

class _MusicLibraryScreenState extends State<MusicLibraryScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: const InputDecoration(
                  hintText: '搜索歌曲、歌手、专辑...',
                  border: InputBorder.none,
                ),
                onChanged: (query) {
                  context.read<MusicProvider>().searchSongs(query);
                },
              )
            : const Text('音乐库'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                if (_isSearching) {
                  _searchController.clear();
                  context.read<MusicProvider>().searchSongs('');
                }
                _isSearching = !_isSearching;
              });
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'scan':
                  _showScanDialog();
                  break;
                case 'refresh':
                  context.read<MusicProvider>().loadSongs();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'scan',
                child: Row(
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: 8),
                    Text('扫描音乐'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.sync),
                    SizedBox(width: 8),
                    Text('刷新列表'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<MusicProvider>(
        builder: (context, musicProvider, child) {
          // 显示扫描进度
          if (musicProvider.isScanning) {
            return _buildScanningView(musicProvider);
          }

          // 获取要显示的歌曲列表
          List<Song> songs = _isSearching && _searchController.text.isNotEmpty
              ? musicProvider.searchResults
              : musicProvider.allSongs;

          if (songs.isEmpty) {
            return _buildEmptyView();
          }

          return _buildSongList(songs, musicProvider);
        },
      ),
    );
  }

  /// 构建扫描进度视图
  Widget _buildScanningView(MusicProvider musicProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 24),
            Text(
              '正在扫描音乐文件...',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: musicProvider.scanProgress / 100,
            ),
            const SizedBox(height: 8),
            Text('${musicProvider.scanProgress}%'),
            const SizedBox(height: 16),
            if (musicProvider.currentScanFile.isNotEmpty)
              Text(
                musicProvider.currentScanFile.split('/').last,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_note,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              _isSearching ? '没有找到相关歌曲' : '暂无音乐文件',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _isSearching ? '尝试使用其他关键词搜索' : '点击右上角菜单扫描设备中的音乐文件',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (!_isSearching) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _showScanDialog,
                icon: const Icon(Icons.refresh),
                label: const Text('扫描音乐'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建歌曲列表
  Widget _buildSongList(List<Song> songs, MusicProvider musicProvider) {
    return ListView.builder(
      itemCount: songs.length,
      itemBuilder: (context, index) {
        final song = songs[index];
        return SongListTile(
          song: song,
          onTap: () {
            musicProvider.playPlaylist(songs, startIndex: index);
          },
          onFavoriteToggle: () {
            musicProvider.toggleSongFavorite(song);
          },
          trailing: PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'add_to_playlist':
                  _showAddToPlaylistDialog(song);
                  break;
                case 'song_info':
                  _showSongInfoDialog(song);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_to_playlist',
                child: Row(
                  children: [
                    Icon(Icons.playlist_add),
                    SizedBox(width: 8),
                    Text('添加到歌单'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'song_info',
                child: Row(
                  children: [
                    Icon(Icons.info),
                    SizedBox(width: 8),
                    Text('歌曲信息'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 显示扫描对话框
  void _showScanDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('扫描音乐文件'),
        content: const Text('这将扫描设备中的音频文件并添加到音乐库中。此过程可能需要一些时间。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MusicProvider>().scanAudioFiles();
            },
            child: const Text('开始扫描'),
          ),
        ],
      ),
    );
  }

  /// 显示添加到歌单对话框
  void _showAddToPlaylistDialog(Song song) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加到歌单'),
        content: const Text('选择要添加到的歌单'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示歌曲信息对话框
  void _showSongInfoDialog(Song song) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('歌曲信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('标题', song.title),
            _buildInfoRow('歌手', song.artist),
            _buildInfoRow('专辑', song.album),
            _buildInfoRow('时长', song.formattedDuration),
            _buildInfoRow('文件路径', song.filePath),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
